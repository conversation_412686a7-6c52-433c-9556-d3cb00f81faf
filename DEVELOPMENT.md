# دليل التطوير - Development Guide

هذا الدليل يوضح كيفية تطوير وتحسين تطبيق أوقات الصلاة.

## الهيكل الحالي

### الملفات الأساسية

1. **`main.py`** - الملف الرئيسي
   - تهيئة التطبيق وQApplication
   - إدارة أيقونة شريط النظام
   - جدولة التنبيهات والأذان
   - حلقة الأحداث الرئيسية

2. **`prayer.py`** - وحدة حساب أوقات الصلاة
   - دعم مكتبة `islam` أو `time-to-pray`
   - طرق حساب متعددة
   - تحويل المناطق الزمنية
   - تنسيق الأوقات

3. **`settings.py`** - نافذة الإعدادات
   - تبويبات للموقع وطريقة الحساب والتنبيهات
   - واجهة مستخدم بـ PyQt6
   - حفظ وتحميل الإعدادات

4. **`utils.py`** - الأدوات المساعدة
   - مدير الإعدادات (ConfigManager)
   - خدمة تحديد الموقع
   - مساعدات الإشعارات والصوت
   - إعداد السجلات

## خطوات التطوير التالية

### المرحلة 1: التنفيذ الأساسي

#### 1.1 تنفيذ `main.py`
- [ ] تنفيذ `_load_settings()` و `_save_settings()`
- [ ] إنشاء `QSystemTrayIcon` مع القائمة
- [ ] تهيئة `QtScheduler` للتنبيهات
- [ ] ربط الإشارات والأحداث

#### 1.2 تنفيذ `prayer.py`
- [ ] تنفيذ دعم مكتبة `islam`
- [ ] حساب أوقات الصلاة الفعلي
- [ ] تحويل المناطق الزمنية
- [ ] تنسيق الأوقات للعرض

#### 1.3 تنفيذ `settings.py`
- [ ] إكمال واجهة تبويب الموقع
- [ ] إكمال واجهة تبويب طريقة الحساب
- [ ] إكمال واجهة تبويب التنبيهات
- [ ] ربط الحفظ والتحميل

#### 1.4 تنفيذ `utils.py`
- [ ] إكمال `ConfigManager`
- [ ] تنفيذ `LocationService`
- [ ] تنفيذ مساعدات الإشعارات
- [ ] إعداد نظام السجلات

### المرحلة 2: الميزات المتقدمة

#### 2.1 تحسين الواجهة
- [ ] إضافة أيقونة مخصصة
- [ ] تحسين تصميم القائمة
- [ ] إضافة رسوم متحركة بسيطة
- [ ] دعم السمات (Themes)

#### 2.2 ميزات إضافية
- [ ] تحديد الموقع التلقائي
- [ ] دعم عدة ملفات أذان
- [ ] إعدادات متقدمة للتنبيهات
- [ ] إحصائيات الصلاة

#### 2.3 التكامل مع النظام
- [ ] تشغيل تلقائي مع النظام
- [ ] دعم StatusNotifierItem لـ KDE
- [ ] اختصارات لوحة المفاتيح
- [ ] دعم سطر الأوامر

### المرحلة 3: التحسين والاختبار

#### 3.1 الأداء
- [ ] تحسين استهلاك الذاكرة
- [ ] تحسين سرعة بدء التشغيل
- [ ] تحسين دقة التوقيت
- [ ] معالجة الأخطاء

#### 3.2 الاختبار
- [ ] اختبارات الوحدة (Unit Tests)
- [ ] اختبارات التكامل
- [ ] اختبار على بيئات مختلفة
- [ ] اختبار الأداء

## إرشادات التطوير

### 1. معايير الكود

#### Python Style
- استخدم PEP 8 لتنسيق الكود
- أضف docstrings للدوال والكلاسات
- استخدم type hints حيثما أمكن
- اكتب تعليقات باللغة العربية للوضوح

#### PyQt6 Best Practices
- استخدم الإشارات والفتحات (Signals & Slots)
- تجنب العمليات الطويلة في الخيط الرئيسي
- استخدم QThread للعمليات الثقيلة
- احرص على تنظيف الموارد

### 2. إدارة الأخطاء

```python
# مثال على معالجة الأخطاء
try:
    # كود قد يفشل
    result = risky_operation()
except SpecificException as e:
    logging.error(f"خطأ محدد: {e}")
    # معالجة مناسبة
except Exception as e:
    logging.error(f"خطأ غير متوقع: {e}")
    # معالجة عامة
```

### 3. السجلات (Logging)

```python
import logging

# في بداية كل ملف
logger = logging.getLogger(__name__)

# استخدام السجلات
logger.info("معلومة مفيدة")
logger.warning("تحذير")
logger.error("خطأ حدث")
logger.debug("معلومة للتطوير")
```

### 4. الاختبار

#### تشغيل الاختبار الأساسي
```bash
python test_basic.py
```

#### إضافة اختبارات جديدة
```python
def test_new_feature():
    """اختبار ميزة جديدة"""
    # ترتيب
    setup_data()
    
    # تنفيذ
    result = new_feature()
    
    # تحقق
    assert result == expected_value
    
    # تنظيف
    cleanup()
```

## أدوات التطوير المفيدة

### 1. محرر النصوص / IDE
- **VS Code** مع إضافات Python و PyQt
- **PyCharm** (Professional أو Community)
- **Vim/Neovim** مع إضافات Python

### 2. أدوات فحص الكود
```bash
# تثبيت أدوات فحص الكود
pip install black flake8 mypy

# تنسيق الكود
black *.py

# فحص جودة الكود
flake8 *.py

# فحص الأنواع
mypy *.py
```

### 3. أدوات التطوير
```bash
# تثبيت أدوات إضافية
pip install pytest pytest-qt pytest-cov

# تشغيل الاختبارات
pytest

# تشغيل مع تغطية الكود
pytest --cov=.
```

## مشاكل شائعة وحلولها

### 1. مشاكل PyQt6
**المشكلة**: `ModuleNotFoundError: No module named 'PyQt6'`
**الحل**: 
```bash
pip install PyQt6
```

### 2. مشاكل شريط النظام
**المشكلة**: الأيقونة لا تظهر في شريط النظام
**الحل**: 
- تأكد من تشغيل بيئة سطح مكتب مدعومة
- تحقق من إعدادات شريط النظام

### 3. مشاكل الصوت
**المشكلة**: لا يتم تشغيل الأذان
**الحل**:
- تأكد من وجود ملف الصوت
- تحقق من إعدادات الصوت في النظام
- جرب تنسيقات صوت مختلفة

### 4. مشاكل أوقات الصلاة
**المشكلة**: أوقات غير دقيقة
**الحل**:
- تأكد من صحة الإحداثيات
- جرب طريقة حساب مختلفة
- تحقق من المنطقة الزمنية

## المساهمة في التطوير

### 1. إعداد بيئة التطوير
```bash
# استنساخ المشروع
git clone <repository-url>
cd prayer-times-app

# إنشاء بيئة افتراضية
python3 -m venv venv
source venv/bin/activate

# تثبيت المتطلبات
pip install -r requirements.txt

# تثبيت أدوات التطوير
pip install -r requirements-dev.txt  # إذا وُجد
```

### 2. سير العمل
1. أنشئ فرع جديد للميزة
2. طوّر الميزة مع الاختبارات
3. تأكد من نجاح جميع الاختبارات
4. أرسل Pull Request

### 3. معايير المراجعة
- الكود يتبع معايير PEP 8
- جميع الاختبارات تنجح
- التوثيق محدث
- لا توجد تحذيرات من أدوات فحص الكود

## الموارد المفيدة

### التوثيق
- [PyQt6 Documentation](https://doc.qt.io/qtforpython/)
- [APScheduler Documentation](https://apscheduler.readthedocs.io/)
- [Python Islam Library](https://pypi.org/project/islam/)

### أمثلة ومراجع
- [Qt System Tray Example](https://doc.qt.io/qt-6/qtwidgets-desktop-systray-example.html)
- [KDE StatusNotifierItem](https://www.freedesktop.org/wiki/Specifications/StatusNotifierItem/)
- [Islamic Prayer Times Calculation](https://en.wikipedia.org/wiki/Salah_times)

---

**ملاحظة**: هذا دليل حي يتم تحديثه مع تطور المشروع.
