# تطبيق أوقات الصلاة لـ KDE Plasma
## Prayer Times Application for KDE Plasma

تطبيق سطح مكتب يعمل في شريط النظام (System Tray) لعرض أوقات الصلاة وتنبيهات الأذان، مصمم خصيصاً لبيئة KDE Plasma على لينكس.

## المميزات

- ✅ **حساب دقيق لأوقات الصلاة** باستخدام مكتبة `islam` أو `time-to-pray`
- ✅ **أيقونة في شريط النظام** مع دعم StatusNotifierItem لـ KDE Plasma
- ✅ **قائمة تفاعلية** تعرض أوقات الصلاة اليومية
- ✅ **تنبيهات قابلة للتخصيص** قبل وقت الصلاة
- ✅ **تشغيل الأذان** مع إمكانية تغيير الملف الصوتي
- ✅ **واجهة إعدادات شاملة** لتخصيص جميع الخيارات
- ✅ **دعم طرق حساب متعددة** (رابطة العالم الإسلامي، أم القرى، إلخ)
- ✅ **دعم المناطق الزمنية** المختلفة
- ✅ **واجهة عربية** مع دعم اللغة الإنجليزية

## متطلبات النظام

- **نظام التشغيل**: Linux (مُحسَّن لـ KDE Plasma)
- **Python**: 3.8 أو أحدث
- **PyQt6**: للواجهة الرسومية
- **مكتبات إضافية**: انظر `requirements.txt`

## التثبيت

### 1. استنساخ المشروع
```bash
git clone <repository-url>
cd prayer-times-app
```

### 2. إنشاء بيئة افتراضية (مُستحسن)
```bash
python3 -m venv venv
source venv/bin/activate  # على Linux/Mac
```

### 3. تثبيت المتطلبات
```bash
pip install -r requirements.txt
```

### 4. إعداد ملف الأذان (اختياري)
ضع ملف الأذان الصوتي في مجلد `assets/` باسم `adhan.mp3` أو قم بتغيير المسار من الإعدادات.

## التشغيل

```bash
python main.py
```

أو يمكنك جعل الملف قابلاً للتنفيذ:
```bash
chmod +x main.py
./main.py
```

## الاستخدام

### الإعداد الأولي
1. **تشغيل التطبيق** - ستظهر أيقونة في شريط النظام
2. **النقر بالزر الأيمن** على الأيقونة واختيار "الإعدادات"
3. **تحديد الموقع** - أدخل خط العرض والطول أو المدينة
4. **اختيار طريقة الحساب** المناسبة لمنطقتك
5. **ضبط التنبيهات** حسب تفضيلاتك

### الميزات الرئيسية
- **عرض أوقات الصلاة**: انقر على الأيقونة لرؤية أوقات اليوم
- **التنبيهات**: ستظهر إشعارات قبل كل صلاة
- **الأذان**: سيتم تشغيل الأذان في وقت كل صلاة
- **الإعدادات**: انقر بالزر الأيمن → "الإعدادات" للتخصيص

## هيكل المشروع

```
prayer-times-app/
├── main.py              # الملف الرئيسي - تهيئة التطبيق وشريط النظام
├── prayer.py            # وحدة حساب أوقات الصلاة
├── settings.py          # نافذة الإعدادات
├── requirements.txt     # متطلبات Python
├── README.md           # هذا الملف
└── assets/             # مجلد الأصول
    ├── config.json     # ملف الإعدادات
    ├── adhan.mp3       # ملف الأذان الصوتي (يُضاف من المستخدم)
    └── icon.png        # أيقونة التطبيق (ستُضاف لاحقاً)
```

## الإعدادات

### الموقع الجغرافي
- **خط العرض والطول**: للحساب الدقيق
- **المدينة والدولة**: للعرض
- **المنطقة الزمنية**: لضبط التوقيت

### طريقة الحساب
- رابطة العالم الإسلامي (افتراضي)
- جامعة أم القرى، مكة
- الهيئة المصرية العامة للمساحة
- وطرق أخرى متعددة

### التنبيهات والأذان
- تفعيل/تعطيل التنبيهات
- وقت التنبيه قبل الصلاة (1-60 دقيقة)
- تفعيل/تعطيل الأذان
- اختيار ملف الأذان المخصص
- ضبط مستوى الصوت

## استكشاف الأخطاء

### المشاكل الشائعة

1. **الأيقونة لا تظهر في شريط النظام**
   - تأكد من تشغيل KDE Plasma
   - تحقق من إعدادات شريط النظام في KDE

2. **لا يتم تشغيل الأذان**
   - تأكد من وجود ملف الأذان في المسار المحدد
   - تحقق من إعدادات الصوت في النظام

3. **أوقات الصلاة غير دقيقة**
   - تأكد من صحة الإحداثيات الجغرافية
   - جرب طريقة حساب مختلفة

### سجلات الأخطاء
يتم حفظ سجلات الأخطاء في:
```
~/.local/share/prayer-times-app/logs/
```

## التطوير

### إضافة ميزات جديدة
1. **إضافة طرق حساب جديدة**: عدّل `prayer.py`
2. **تخصيص الواجهة**: عدّل `settings.py`
3. **إضافة لغات جديدة**: أضف ملفات الترجمة

### الاختبار
```bash
# تشغيل الاختبارات (عند إضافتها)
pytest tests/
```

## المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء فرع للميزة الجديدة
3. إرسال Pull Request

## الترخيص

هذا المشروع مرخص تحت [رخصة MIT](LICENSE).

## الدعم

للحصول على الدعم أو الإبلاغ عن مشاكل:
- افتح Issue في GitHub
- راسلنا على [البريد الإلكتروني]

---

**ملاحظة**: هذا التطبيق في مرحلة التطوير. بعض الميزات قد تحتاج إلى تحسينات إضافية.
