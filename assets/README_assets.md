# مجلد الأصول - Assets Directory

هذا المجلد يحتوي على الملفات المساعدة للتطبيق.

## الملفات المطلوبة

### 1. ملف الإعدادات
- **الملف**: `config.json`
- **الوصف**: ملف الإعدادات الافتراضية للتطبيق
- **الحالة**: ✅ موجود

### 2. ملف الأذان الصوتي
- **الملف**: `adhan.mp3` (أو أي تنسيق صوتي مدعوم)
- **الوصف**: ملف صوتي للأذان يتم تشغيله في أوقات الصلاة
- **الحالة**: ⚠️ يجب إضافته من المستخدم
- **التنسيقات المدعومة**: MP3, WAV, OGG, M4A, FLAC

#### كيفية إضافة ملف الأذان:
1. احصل على ملف أذان صوتي بجودة جيدة
2. ضعه في هذا المجلد باسم `adhan.mp3`
3. أو ضعه بأي اسم وغيّر المسار من الإعدادات

#### مصادر مقترحة لملفات الأذان:
- تسجيلات من الحرم المكي أو المدني
- تسجيلات محلية من المساجد
- ملفات أذان مجانية من الإنترنت

### 3. أيقونة التطبيق
- **الملف**: `icon.png` (أو `icon.svg`)
- **الوصف**: أيقونة التطبيق التي تظهر في شريط النظام
- **الحالة**: ⚠️ يجب إضافتها
- **المتطلبات**: 
  - الحجم المفضل: 64x64 بكسل أو أكبر
  - التنسيق: PNG أو SVG
  - خلفية شفافة مفضلة

#### اقتراحات للأيقونة:
- رمز مسجد بسيط
- رمز هلال وقبة
- رمز ساعة مع رموز إسلامية
- نص عربي بسيط مثل "صلاة"

### 4. ملفات إضافية (اختيارية)

#### أصوات إضافية
يمكن إضافة ملفات صوتية إضافية:
- `notification.mp3` - صوت التنبيه قبل الصلاة
- `fajr_adhan.mp3` - أذان خاص بالفجر
- `other_prayers_adhan.mp3` - أذان باقي الصلوات

#### ملفات الترجمة (مستقبلياً)
- `translations/` - مجلد ملفات الترجمة
- `ar.json` - الترجمة العربية
- `en.json` - الترجمة الإنجليزية

#### ملفات التخصيص
- `themes/` - مجلد السمات المخصصة
- `custom_methods.json` - طرق حساب مخصصة

## هيكل المجلد النهائي

```
assets/
├── README_assets.md          # هذا الملف
├── config.json              # ✅ الإعدادات الافتراضية
├── adhan.mp3               # ⚠️ ملف الأذان (يُضاف من المستخدم)
├── icon.png                # ⚠️ أيقونة التطبيق (تُضاف من المستخدم)
├── notification.mp3        # 🔄 صوت التنبيه (اختياري)
└── translations/           # 🔄 ملفات الترجمة (مستقبلياً)
    ├── ar.json
    └── en.json
```

## ملاحظات مهمة

1. **حقوق الطبع والنشر**: تأكد من أن الملفات الصوتية التي تستخدمها لا تنتهك حقوق الطبع والنشر
2. **جودة الصوت**: استخدم ملفات صوتية بجودة جيدة لتجربة أفضل
3. **حجم الملفات**: تجنب الملفات الكبيرة جداً لتوفير مساحة التخزين
4. **التوافق**: تأكد من أن الملفات متوافقة مع مشغل Qt الصوتي

## إنشاء أيقونة بسيطة

إذا لم تكن لديك أيقونة، يمكنك إنشاء واحدة بسيطة:

### باستخدام GIMP أو Inkscape:
1. أنشئ صورة 64x64 بكسل
2. ارسم رمز مسجد بسيط أو هلال
3. استخدم ألوان هادئة (أزرق، أخضر، ذهبي)
4. احفظ كـ PNG مع خلفية شفافة

### باستخدام أدوات النص:
1. اكتب نص "صلاة" أو "🕌" بخط كبير
2. احفظ كصورة PNG

### أيقونات جاهزة:
يمكن البحث عن أيقونات مجانية في:
- Flaticon
- Icons8  
- Font Awesome
- Material Design Icons

تأكد من التحقق من الترخيص قبل الاستخدام.
