#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء أيقونة بسيطة للتطبيق
Create Simple Icon for the Application
"""

from PyQt6.QtGui import QPixmap, QPainter, QBrush, QColor, QPen, QFont
from PyQt6.QtCore import Qt
import sys

def create_prayer_icon(size=64):
    """إنشاء أيقونة بسيطة لتطبيق أوقات الصلاة"""
    
    # إنشاء pixmap شفاف
    pixmap = QPixmap(size, size)
    pixmap.fill(Qt.GlobalColor.transparent)
    
    # إنشاء painter
    painter = QPainter(pixmap)
    painter.setRenderHint(QPainter.RenderHint.Antialiasing)
    
    # رسم خلفية دائرية
    painter.setBrush(QBrush(QColor(0, 120, 0, 200)))  # أخضر شفاف
    painter.setPen(QPen(QColor(0, 80, 0), 2))
    painter.drawEllipse(4, 4, size-8, size-8)
    
    # رسم هلال بسيط
    painter.setBrush(QBrush(QColor(255, 255, 255)))
    painter.setPen(QPen(QColor(200, 200, 200), 1))
    
    # الهلال الخارجي
    painter.drawEllipse(size//4, size//6, size//2, size//2)
    
    # الهلال الداخلي (لإنشاء شكل الهلال)
    painter.setBrush(QBrush(QColor(0, 120, 0, 200)))
    painter.drawEllipse(size//3, size//5, size//3, size//3)
    
    # إضافة نجمة صغيرة
    painter.setBrush(QBrush(QColor(255, 255, 255)))
    star_size = size // 8
    star_x = size * 3 // 4
    star_y = size // 4
    painter.drawEllipse(star_x, star_y, star_size, star_size)
    
    # إضافة نص عربي صغير
    font = QFont("Arial", size//8)
    painter.setFont(font)
    painter.setPen(QPen(QColor(255, 255, 255)))
    painter.drawText(size//6, size*4//5, "صلاة")
    
    painter.end()
    
    return pixmap

def main():
    """إنشاء وحفظ الأيقونة"""
    from PyQt6.QtWidgets import QApplication
    
    app = QApplication(sys.argv)
    
    # إنشاء أيقونات بأحجام مختلفة
    sizes = [16, 32, 48, 64, 128]
    
    for size in sizes:
        pixmap = create_prayer_icon(size)
        filename = f"assets/icon_{size}x{size}.png"
        pixmap.save(filename)
        print(f"تم إنشاء الأيقونة: {filename}")
    
    # إنشاء أيقونة افتراضية
    default_pixmap = create_prayer_icon(64)
    default_pixmap.save("assets/icon.png")
    print("تم إنشاء الأيقونة الافتراضية: assets/icon.png")
    
    print("تم إنشاء جميع الأيقونات بنجاح!")

if __name__ == "__main__":
    main()
