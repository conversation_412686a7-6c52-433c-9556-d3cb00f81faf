#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
إنشاء ملف صوتي تجريبي للأذان
Create Test Audio File for Adhan
"""

import numpy as np
import wave
import sys

def create_simple_tone(frequency, duration, sample_rate=44100):
    """إنشاء نغمة بسيطة"""
    t = np.linspace(0, duration, int(sample_rate * duration), False)
    # إنشاء موجة جيبية بسيطة
    wave_data = np.sin(2 * np.pi * frequency * t)
    
    # تطبيق تأثير fade in/out
    fade_samples = int(0.1 * sample_rate)  # 0.1 ثانية fade
    wave_data[:fade_samples] *= np.linspace(0, 1, fade_samples)
    wave_data[-fade_samples:] *= np.linspace(1, 0, fade_samples)
    
    return wave_data

def create_adhan_melody():
    """إنشاء لحن بسيط يحاكي الأذان"""
    sample_rate = 44100
    
    # نغمات بسيطة تحاكي الأذان
    notes = [
        (440, 0.8),   # A4
        (493, 0.8),   # B4
        (523, 1.2),   # C5
        (493, 0.8),   # B4
        (440, 0.8),   # A4
        (392, 1.0),   # G4
        (440, 1.5),   # A4
    ]
    
    # دمج النغمات
    full_audio = np.array([])
    
    for frequency, duration in notes:
        tone = create_simple_tone(frequency, duration, sample_rate)
        full_audio = np.concatenate([full_audio, tone])
        
        # إضافة فترة صمت قصيرة بين النغمات
        silence = np.zeros(int(0.1 * sample_rate))
        full_audio = np.concatenate([full_audio, silence])
    
    # تطبيع الصوت
    full_audio = full_audio / np.max(np.abs(full_audio))
    
    # تحويل إلى 16-bit
    audio_16bit = (full_audio * 32767).astype(np.int16)
    
    return audio_16bit, sample_rate

def save_wav_file(filename, audio_data, sample_rate):
    """حفظ الملف الصوتي بصيغة WAV"""
    with wave.open(filename, 'w') as wav_file:
        wav_file.setnchannels(1)  # mono
        wav_file.setsampwidth(2)  # 16-bit
        wav_file.setframerate(sample_rate)
        wav_file.writeframes(audio_data.tobytes())

def main():
    """إنشاء ملف الأذان التجريبي"""
    try:
        print("إنشاء ملف أذان تجريبي...")
        
        # إنشاء اللحن
        audio_data, sample_rate = create_adhan_melody()
        
        # حفظ الملف
        filename = "assets/adhan.wav"
        save_wav_file(filename, audio_data, sample_rate)
        
        print(f"تم إنشاء ملف الأذان التجريبي: {filename}")
        print("ملاحظة: هذا ملف تجريبي بسيط. يمكنك استبداله بملف أذان حقيقي.")
        
    except ImportError:
        print("تحذير: مكتبة numpy غير متاحة.")
        print("لإنشاء ملف أذان تجريبي، قم بتثبيت numpy:")
        print("pip install numpy")
        print("")
        print("أو ضع ملف أذان حقيقي في assets/adhan.mp3")
        
        # إنشاء ملف نصي بدلاً من ذلك
        with open("assets/adhan_placeholder.txt", "w", encoding="utf-8") as f:
            f.write("ضع ملف الأذان هنا باسم adhan.mp3 أو adhan.wav\n")
            f.write("يمكن تحميل ملفات الأذان من:\n")
            f.write("- تسجيلات الحرم المكي\n")
            f.write("- تسجيلات الحرم المدني\n")
            f.write("- مواقع الأذان المجانية\n")
        
        print("تم إنشاء ملف إرشادي في assets/adhan_placeholder.txt")

if __name__ == "__main__":
    main()
