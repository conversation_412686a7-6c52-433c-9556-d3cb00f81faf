#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
تطبيق أوقات الصلاة لـ KDE Plasma
Prayer Times Application for KDE Plasma

الملف الرئيسي - يحتوي على:
- تهيئة التطبيق
- إعداد أيقونة شريط النظام (StatusNotifierItem)
- تحميل وحفظ الإعدادات
- جدولة التنبيهات والأذان
- إدارة دورة حياة التطبيق
"""

import sys
import json
import os
from pathlib import Path
from datetime import datetime, timedelta
from typing import Dict, Any, Optional

from PyQt6.QtWidgets import (
    QApplication, QSystemTrayIcon, QMenu, QMessageBox
)
from PyQt6.QtCore import QTimer, QObject, pyqtSignal, QThread
from PyQt6.QtGui import QIcon, QAction
from PyQt6.QtMultimedia import QMediaPlayer, QAudioOutput

from apscheduler.schedulers.qt import QtScheduler
from apscheduler.triggers.date import DateTrigger

# استيراد الوحدات المحلية
from prayer import PrayerTimesCalculator
from settings import SettingsWindow


class PrayerApp(QObject):
    """
    الكلاس الرئيسي لتطبيق أوقات الصلاة
    يدير أيقونة شريط النظام والتنبيهات
    """
    
    # إشارات للتواصل بين المكونات
    prayer_time_updated = pyqtSignal(dict)
    notification_triggered = pyqtSignal(str, str)
    
    def __init__(self):
        super().__init__()
        
        # مسارات الملفات
        self.app_dir = Path(__file__).parent
        self.assets_dir = self.app_dir / "assets"
        self.config_file = self.app_dir / "config.json"
        
        # المكونات الأساسية
        self.tray_icon = None
        self.tray_menu = None
        self.settings_window = None
        self.prayer_calculator = None
        self.scheduler = None
        self.media_player = None
        self.audio_output = None
        
        # الإعدادات الافتراضية
        self.default_settings = {
            "location": {
                "latitude": 31.7683,  # الرياض كمثال
                "longitude": 35.2137,
                "city": "الرياض",
                "country": "السعودية",
                "timezone": "Asia/Riyadh"
            },
            "calculation_method": "muslim_league",
            "notification_minutes": 5,
            "enable_notifications": True,
            "enable_adhan": True,
            "adhan_sound_file": "assets/adhan.mp3",
            "language": "ar"
        }
        
        self.settings = self.default_settings.copy()
        
        # TODO: تهيئة المكونات
        self._init_components()
    
    def _init_components(self):
        """تهيئة جميع مكونات التطبيق"""
        # TODO: تحميل الإعدادات من الملف
        self._load_settings()
        
        # TODO: إنشاء مجلد الأصول إذا لم يكن موجوداً
        self._ensure_assets_directory()
        
        # TODO: تهيئة حاسبة أوقات الصلاة
        self._init_prayer_calculator()
        
        # TODO: تهيئة مشغل الصوت
        self._init_media_player()
        
        # TODO: تهيئة المجدول
        self._init_scheduler()
        
        # TODO: تهيئة أيقونة شريط النظام
        self._init_system_tray()
        
        # TODO: جدولة التنبيهات اليومية
        self._schedule_daily_prayers()
    
    def _load_settings(self):
        """تحميل الإعدادات من ملف JSON"""
        from utils import ConfigManager

        config_manager = ConfigManager()
        loaded_settings = config_manager.load_config()

        # دمج الإعدادات المحملة مع الافتراضية بشكل عميق
        self._deep_update(self.settings, loaded_settings)

        print(f"الإعدادات المحملة: {self.settings}")

    def _deep_update(self, base_dict, update_dict):
        """تحديث عميق للقاموس"""
        for key, value in update_dict.items():
            if key in base_dict and isinstance(base_dict[key], dict) and isinstance(value, dict):
                self._deep_update(base_dict[key], value)
            else:
                base_dict[key] = value
    
    def _save_settings(self):
        """حفظ الإعدادات في ملف JSON"""
        from utils import ConfigManager

        config_manager = ConfigManager()
        config_manager.save_config(self.settings)
    
    def _ensure_assets_directory(self):
        """التأكد من وجود مجلد الأصول"""
        self.assets_dir.mkdir(exist_ok=True)
    
    def _init_prayer_calculator(self):
        """تهيئة حاسبة أوقات الصلاة"""
        location = self.settings.get("location", {})
        calculation = self.settings.get("calculation", {})

        # استخدام القيم الافتراضية من الإعدادات الافتراضية
        default_location = self.default_settings["location"]

        self.prayer_calculator = PrayerTimesCalculator(
            latitude=location.get("latitude", default_location["latitude"]),
            longitude=location.get("longitude", default_location["longitude"]),
            timezone=location.get("timezone", default_location["timezone"]),
            calculation_method=calculation.get("method", self.default_settings["calculation_method"])
        )
    
    def _init_media_player(self):
        """تهيئة مشغل الصوت للأذان"""
        self.media_player = QMediaPlayer()
        self.audio_output = QAudioOutput()
        self.media_player.setAudioOutput(self.audio_output)

    def _init_scheduler(self):
        """تهيئة مجدول المهام"""
        self.scheduler = QtScheduler()
        self.scheduler.start()
    
    def _init_system_tray(self):
        """تهيئة أيقونة شريط النظام والقائمة"""
        # إنشاء أيقونة شريط النظام
        self.tray_icon = QSystemTrayIcon(self)

        # تعيين أيقونة التطبيق
        from PyQt6.QtGui import QIcon, QPixmap, QPainter, QBrush, QColor
        from PyQt6.QtCore import Qt

        icon_path = self.assets_dir / "icon.png"
        if icon_path.exists():
            icon = QIcon(str(icon_path))
        else:
            # إنشاء أيقونة بسيطة إذا لم توجد
            pixmap = QPixmap(64, 64)
            pixmap.fill(Qt.GlobalColor.transparent)
            painter = QPainter(pixmap)
            painter.setBrush(QBrush(QColor(0, 150, 0)))
            painter.drawEllipse(8, 8, 48, 48)
            painter.end()
            icon = QIcon(pixmap)
        self.tray_icon.setIcon(icon)

        # إنشاء القائمة
        self._create_tray_menu()

        # عرض الأيقونة
        self.tray_icon.show()

        # ربط النقر على الأيقونة
        self.tray_icon.activated.connect(self._on_tray_icon_activated)
    
    def _create_tray_menu(self):
        """إنشاء قائمة أيقونة شريط النظام المتقدمة"""
        self.tray_menu = QMenu()

        # تطبيق تنسيق مشابه للتطبيق المرجعي
        self.tray_menu.setStyleSheet("""
            QMenu {
                background-color: #f8f9fa;
                border: 1px solid #dee2e6;
                border-radius: 8px;
                padding: 8px;
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 11px;
                min-width: 280px;
            }
            QMenu::item {
                background-color: transparent;
                padding: 6px 12px;
                border-radius: 4px;
                margin: 1px;
            }
            QMenu::item:selected {
                background-color: #e9ecef;
            }
            QMenu::item:disabled {
                color: #6c757d;
            }
            QMenu::separator {
                height: 1px;
                background-color: #dee2e6;
                margin: 4px 8px;
            }
        """)

        # عنوان القائمة مع التاريخ
        self._add_header_section()

        # أوقات الصلاة مع العد التنازلي
        self._add_prayer_times_section()

        # معلومات إضافية (الموقع، التاريخ الهجري)
        self._add_info_section()

        # أزرار التحكم
        self._add_control_buttons()

        # تعيين القائمة للأيقونة
        self.tray_icon.setContextMenu(self.tray_menu)

        # تحديث دوري للعد التنازلي
        self.countdown_timer = QTimer()
        self.countdown_timer.timeout.connect(self._update_countdown)
        self.countdown_timer.start(1000)  # تحديث كل ثانية

    def _add_header_section(self):
        """إضافة قسم العنوان والتاريخ"""
        # عنوان التطبيق
        title_action = QAction("🕌 أوقات الصلاة", self)
        title_action.setEnabled(False)
        font = title_action.font()
        font.setBold(True)
        font.setPointSize(12)
        title_action.setFont(font)
        self.tray_menu.addAction(title_action)

        # التاريخ الميلادي
        today = datetime.now()
        date_str = today.strftime("%A, %d %B %Y")
        date_action = QAction(f"📅 {date_str}", self)
        date_action.setEnabled(False)
        self.tray_menu.addAction(date_action)

        # التاريخ الهجري
        self.hijri_date_action = QAction("📅 التاريخ الهجري: جاري التحميل...", self)
        self.hijri_date_action.setEnabled(False)
        self.tray_menu.addAction(self.hijri_date_action)

        self.tray_menu.addSeparator()

    def _add_prayer_times_section(self):
        """إضافة قسم أوقات الصلاة مع العد التنازلي"""
        # العد التنازلي للصلاة التالية
        self.countdown_action = QAction("⏰ العد التنازلي: جاري الحساب...", self)
        self.countdown_action.setEnabled(False)
        font = self.countdown_action.font()
        font.setBold(True)
        self.countdown_action.setFont(font)
        self.tray_menu.addAction(self.countdown_action)

        self.tray_menu.addSeparator()

        # أوقات الصلاة مع حالات مختلفة
        self.prayer_actions = {}
        prayer_names = ['fajr', 'dhuhr', 'asr', 'maghrib', 'isha']

        for prayer in prayer_names:
            arabic_name = PrayerTimesCalculator.get_prayer_name_arabic(prayer)
            action = QAction(f"{arabic_name}: --:--", self)
            action.setEnabled(False)
            self.prayer_actions[prayer] = action
            self.tray_menu.addAction(action)

        self.tray_menu.addSeparator()

    def _add_info_section(self):
        """إضافة قسم المعلومات الإضافية"""
        # الموقع الحالي
        location = self.settings.get("location", {})
        city = location.get("city", "غير محدد")
        country = location.get("country", "")
        location_text = f"{city} - {country}" if country else city

        self.location_action = QAction(f"📍 {location_text}", self)
        self.location_action.setEnabled(False)
        self.tray_menu.addAction(self.location_action)

        self.tray_menu.addSeparator()

    def _add_control_buttons(self):
        """إضافة أزرار التحكم"""
        # تحديث أوقات الصلاة
        update_action = QAction("🔄 تحديث الأوقات", self)
        update_action.triggered.connect(self.update_prayer_times)
        self.tray_menu.addAction(update_action)

        # الإعدادات
        settings_action = QAction("⚙️ الإعدادات", self)
        settings_action.triggered.connect(self.show_settings)
        self.tray_menu.addAction(settings_action)

        self.tray_menu.addSeparator()

        # الخروج
        quit_action = QAction("❌ خروج", self)
        quit_action.triggered.connect(self.quit_application)
        self.tray_menu.addAction(quit_action)
    
    def _schedule_daily_prayers(self):
        """جدولة تنبيهات الصلاة اليومية"""
        # سيتم تنفيذها لاحقاً مع المجدول
        pass

    def _schedule_prayer_notification(self, prayer_name: str, prayer_time: datetime):
        """جدولة تنبيه لصلاة معينة"""
        # سيتم تنفيذها لاحقاً
        pass

    def _schedule_adhan(self, prayer_name: str, prayer_time: datetime):
        """جدولة تشغيل الأذان في وقت الصلاة"""
        # سيتم تنفيذها لاحقاً
        pass

    def show_prayer_notification(self, prayer_name: str):
        """عرض تنبيه الصلاة"""
        arabic_name = PrayerTimesCalculator.get_prayer_name_arabic(prayer_name)
        if self.tray_icon:
            self.tray_icon.showMessage(
                "🕌 وقت الصلاة",
                f"حان وقت صلاة {arabic_name}",
                QSystemTrayIcon.MessageIcon.Information,
                5000  # 5 ثوان
            )

    def play_adhan(self, prayer_name: str):
        """تشغيل صوت الأذان"""
        adhan_file = self.settings.get("notifications", {}).get("adhan_sound_file")
        if adhan_file and Path(adhan_file).exists() and self.media_player:
            from PyQt6.QtCore import QUrl
            self.media_player.setSource(QUrl.fromLocalFile(str(Path(adhan_file).absolute())))
            self.media_player.play()
    
    def _update_countdown(self):
        """تحديث العد التنازلي للصلاة التالية"""
        if not self.prayer_calculator:
            return

        try:
            next_prayer_info = self._get_next_prayer_info()
            if next_prayer_info:
                prayer_name, time_remaining = next_prayer_info
                arabic_name = PrayerTimesCalculator.get_prayer_name_arabic(prayer_name)

                if time_remaining.total_seconds() > 0:
                    hours = int(time_remaining.total_seconds() // 3600)
                    minutes = int((time_remaining.total_seconds() % 3600) // 60)

                    if hours > 0:
                        countdown_text = f"⏰ تبقى {hours}:{minutes:02d} على {arabic_name}"
                    else:
                        countdown_text = f"⏰ تبقى {minutes} دقيقة على {arabic_name}"
                else:
                    countdown_text = f"⏰ حان وقت {arabic_name}"

                self.countdown_action.setText(countdown_text)
            else:
                self.countdown_action.setText("⏰ العد التنازلي: غير متاح")

        except Exception as e:
            print(f"خطأ في تحديث العد التنازلي: {e}")

    def _get_next_prayer_info(self):
        """الحصول على معلومات الصلاة التالية"""
        try:
            if not self.prayer_calculator:
                return None

            prayer_times = self.prayer_calculator.get_prayer_times()
            if not prayer_times:
                return None

            # الحصول على الوقت الحالي مع المنطقة الزمنية
            import pytz
            tz = pytz.timezone(self.prayer_calculator.timezone)
            now = datetime.now(tz)

            # ترتيب الصلوات حسب الوقت
            prayer_order = ['fajr', 'dhuhr', 'asr', 'maghrib', 'isha']

            for prayer in prayer_order:
                if prayer in prayer_times:
                    prayer_time = prayer_times[prayer]
                    if prayer_time and prayer_time > now:
                        time_remaining = prayer_time - now
                        return prayer, time_remaining

            # إذا لم نجد صلاة اليوم، نبحث عن فجر الغد
            tomorrow = now + timedelta(days=1)
            tomorrow_prayer_times = self.prayer_calculator.get_prayer_times(tomorrow.date())
            if tomorrow_prayer_times and 'fajr' in tomorrow_prayer_times:
                fajr_time = tomorrow_prayer_times['fajr']
                if fajr_time:
                    time_remaining = fajr_time - now
                    return 'fajr', time_remaining

        except Exception as e:
            print(f"خطأ في حساب الصلاة التالية: {e}")

        return None

    def _update_hijri_date(self):
        """تحديث التاريخ الهجري"""
        try:
            # استخدام مكتبة hijri-converter إذا كانت متاحة
            try:
                from hijri_converter import Gregorian
                today = datetime.now().date()
                hijri_date = Gregorian(today.year, today.month, today.day).to_hijri()

                # أسماء الأشهر الهجرية
                hijri_months = [
                    "محرم", "صفر", "ربيع الأول", "ربيع الثاني", "جمادى الأولى", "جمادى الثانية",
                    "رجب", "شعبان", "رمضان", "شوال", "ذو القعدة", "ذو الحجة"
                ]

                month_name = hijri_months[hijri_date.month - 1]
                hijri_text = f"📅 {hijri_date.day} {month_name} {hijri_date.year} هـ"

            except ImportError:
                # حساب تقريبي للتاريخ الهجري
                today = datetime.now().date()
                # الفرق التقريبي بين التقويمين (622 سنة و 6 أشهر)
                hijri_year = today.year - 622
                hijri_text = f"📅 التاريخ الهجري: {hijri_year} هـ (تقريبي)"

            self.hijri_date_action.setText(hijri_text)

        except Exception as e:
            print(f"خطأ في حساب التاريخ الهجري: {e}")
            self.hijri_date_action.setText("📅 التاريخ الهجري: غير متاح")

    def update_prayer_times(self):
        """تحديث أوقات الصلاة في القائمة"""
        if not self.prayer_calculator:
            print("حاسبة أوقات الصلاة غير متاحة")
            return

        try:
            # حساب أوقات الصلاة لليوم الحالي
            prayer_times = self.prayer_calculator.get_prayer_times()
            print(f"أوقات الصلاة المحسوبة: {prayer_times}")

            if not prayer_times:
                print("لم يتم الحصول على أوقات الصلاة")
                return

            formatted_times = self.prayer_calculator.format_prayer_times(prayer_times)
            print(f"أوقات الصلاة المنسقة: {formatted_times}")

            # تحديث التاريخ الهجري
            self._update_hijri_date()

            # تحديث أوقات الصلاة مع إضافة حالات مختلفة
            now = datetime.now()
            current_time = now.time()

            for prayer, action in self.prayer_actions.items():
                if prayer in formatted_times and formatted_times[prayer] != "--:--":
                    arabic_name = PrayerTimesCalculator.get_prayer_name_arabic(prayer)
                    time_str = formatted_times[prayer]

                    # تحديد حالة الصلاة
                    prayer_status = self._get_prayer_status(prayer, formatted_times, current_time)

                    if prayer_status == "current":
                        # الصلاة الحالية
                        action.setText(f"🔔 {arabic_name}: {time_str}")
                    elif prayer_status == "missed":
                        # صلاة فائتة
                        action.setText(f"⚠️ {arabic_name}: {time_str}")
                    elif prayer_status == "upcoming":
                        # الصلاة التالية
                        action.setText(f"⏰ {arabic_name}: {time_str}")
                    else:
                        # صلاة عادية
                        action.setText(f"✅ {arabic_name}: {time_str}")
                else:
                    arabic_name = PrayerTimesCalculator.get_prayer_name_arabic(prayer)
                    action.setText(f"{arabic_name}: --:--")

        except Exception as e:
            import traceback
            print(f"خطأ في تحديث أوقات الصلاة: {e}")
            print(f"تفاصيل الخطأ: {traceback.format_exc()}")

    def _get_prayer_status(self, prayer_name, prayer_times, current_time):
        """تحديد حالة الصلاة (حالية، فائتة، قادمة، إلخ)"""
        try:
            # هذه دالة مبسطة - يمكن تطويرها أكثر
            next_prayer_info = self._get_next_prayer_info()
            if next_prayer_info and next_prayer_info[0] == prayer_name:
                return "upcoming"
            return "normal"
        except:
            return "normal"
    
    def show_settings(self):
        """عرض نافذة الإعدادات"""
        if not self.settings_window:
            self.settings_window = SettingsWindow(self.settings)
            self.settings_window.settings_changed.connect(self.on_settings_changed)

        self.settings_window.show()
        self.settings_window.raise_()
        self.settings_window.activateWindow()
    
    def on_settings_changed(self, new_settings: Dict[str, Any]):
        """معالج تغيير الإعدادات"""
        # تحديث الإعدادات
        self.settings.update(new_settings)

        # حفظ الإعدادات
        self._save_settings()

        # إعادة تهيئة حاسبة أوقات الصلاة
        self._init_prayer_calculator()

        # تحديث أوقات الصلاة في القائمة
        self.update_prayer_times()
    
    def quit_application(self):
        """إنهاء التطبيق"""
        # حفظ الإعدادات قبل الخروج
        self._save_settings()

        # إخفاء الأيقونة
        if self.tray_icon:
            self.tray_icon.hide()

        # إنهاء التطبيق
        QApplication.quit()

    def _on_tray_icon_activated(self, reason):
        """معالج النقر على أيقونة شريط النظام"""
        if reason == QSystemTrayIcon.ActivationReason.Trigger:
            # النقر الأيسر - تحديث الأوقات
            self.update_prayer_times()
        elif reason == QSystemTrayIcon.ActivationReason.DoubleClick:
            # النقر المزدوج - فتح الإعدادات
            self.show_settings()


def main():
    """الدالة الرئيسية لتشغيل التطبيق"""
    # تهيئة QApplication
    app = QApplication(sys.argv)

    # تعيين معلومات التطبيق
    app.setApplicationName("تطبيق أوقات الصلاة")
    app.setApplicationVersion("1.0.0")
    app.setOrganizationName("Prayer Times App")

    # التحقق من دعم أيقونة شريط النظام
    if not QSystemTrayIcon.isSystemTrayAvailable():
        QMessageBox.critical(
            None,
            "خطأ في النظام",
            "شريط النظام غير متاح في هذا النظام"
        )
        sys.exit(1)

    # منع إنهاء التطبيق عند إغلاق آخر نافذة
    app.setQuitOnLastWindowClosed(False)

    # إنشاء وتشغيل التطبيق
    prayer_app = PrayerApp()

    # تحديث أوقات الصلاة عند البدء
    prayer_app.update_prayer_times()

    # تشغيل حلقة الأحداث
    sys.exit(app.exec())


if __name__ == "__main__":
    main()
