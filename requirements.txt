# متطلبات تطبيق أوقات الصلاة
# Prayer Times Application Requirements

# واجهة المستخدم الرسومية - GUI Framework
PyQt6>=6.4.0

# حساب أوقات الصلاة - Prayer Times Calculation
# يمكن استخدام إحدى المكتبتين التاليتين:
islam>=1.0.0                    # مكتبة islam الأساسية
# time-to-pray>=1.0.0           # بديل: مكتبة time-to-pray

# جدولة المهام - Task Scheduling
APScheduler>=3.10.0

# التعامل مع المناطق الزمنية - Timezone Handling
pytz>=2023.3

# تشغيل الملفات الصوتية - Audio Playback (مدمج في PyQt6)
# PyQt6 يحتوي على QMediaPlayer و QAudioOutput

# طلبات HTTP للحصول على الموقع الجغرافي (اختياري)
requests>=2.31.0

# تحليل ملفات JSON/YAML للإعدادات
# json مدمج في Python
# yaml اختياري إذا أردنا دعم YAML
# PyYAML>=6.0

# مكتبات إضافية للتطوير والاختبار (اختيارية)
# pytest>=7.0.0               # للاختبارات
# black>=23.0.0               # لتنسيق الكود
# flake8>=6.0.0               # لفحص جودة الكود
