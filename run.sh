#!/bin/bash
# سكريبت تشغيل تطبيق أوقات الصلاة
# Prayer Times Application Launcher Script

# ألوان للإخراج
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# دالة طباعة ملونة
print_colored() {
    echo -e "${2}${1}${NC}"
}

# عرض معلومات التطبيق
print_colored "=== تطبيق أوقات الصلاة لـ KDE Plasma ===" "$BLUE"
print_colored "Prayer Times Application for KDE Plasma" "$BLUE"
echo

# التحقق من Python
print_colored "التحقق من Python..." "$YELLOW"
if ! command -v python3 &> /dev/null; then
    print_colored "خطأ: Python 3 غير مثبت" "$RED"
    exit 1
fi

PYTHON_VERSION=$(python3 --version 2>&1 | cut -d' ' -f2)
print_colored "تم العثور على Python $PYTHON_VERSION" "$GREEN"

# التحقق من pip
if ! command -v pip3 &> /dev/null; then
    print_colored "تحذير: pip3 غير متاح، قد تحتاج لتثبيت المتطلبات يدوياً" "$YELLOW"
fi

# التحقق من بيئة سطح المكتب
DESKTOP_ENV=${XDG_CURRENT_DESKTOP:-"غير محدد"}
print_colored "بيئة سطح المكتب: $DESKTOP_ENV" "$BLUE"

if [[ "$DESKTOP_ENV" == *"KDE"* ]]; then
    print_colored "✓ KDE Plasma مكتشف - التطبيق محسن لهذه البيئة" "$GREEN"
elif [[ "$DESKTOP_ENV" == *"GNOME"* ]]; then
    print_colored "⚠ GNOME مكتشف - قد تحتاج لتمكين إضافات شريط النظام" "$YELLOW"
else
    print_colored "⚠ بيئة سطح مكتب غير مدعومة رسمياً" "$YELLOW"
fi

echo

# التحقق من وجود البيئة الافتراضية
if [[ "$VIRTUAL_ENV" != "" ]]; then
    print_colored "✓ تعمل في بيئة افتراضية: $(basename $VIRTUAL_ENV)" "$GREEN"
else
    print_colored "⚠ لا تعمل في بيئة افتراضية" "$YELLOW"
    print_colored "يُنصح بإنشاء بيئة افتراضية:" "$YELLOW"
    print_colored "  python3 -m venv venv" "$YELLOW"
    print_colored "  source venv/bin/activate" "$YELLOW"
    echo
fi

# التحقق من المتطلبات
print_colored "التحقق من المتطلبات..." "$YELLOW"

# قائمة المتطلبات الأساسية
REQUIRED_PACKAGES=("PyQt6" "APScheduler" "pytz" "requests")
MISSING_PACKAGES=()

for package in "${REQUIRED_PACKAGES[@]}"; do
    if python3 -c "import $package" 2>/dev/null; then
        print_colored "✓ $package مثبت" "$GREEN"
    else
        print_colored "✗ $package غير مثبت" "$RED"
        MISSING_PACKAGES+=("$package")
    fi
done

# تثبيت المتطلبات المفقودة
if [ ${#MISSING_PACKAGES[@]} -gt 0 ]; then
    echo
    print_colored "المتطلبات المفقودة: ${MISSING_PACKAGES[*]}" "$RED"
    
    if [[ "$1" == "--install" ]] || [[ "$1" == "-i" ]]; then
        print_colored "تثبيت المتطلبات..." "$YELLOW"
        pip3 install -r requirements.txt
        
        if [ $? -eq 0 ]; then
            print_colored "✓ تم تثبيت المتطلبات بنجاح" "$GREEN"
        else
            print_colored "✗ فشل في تثبيت المتطلبات" "$RED"
            exit 1
        fi
    else
        print_colored "لتثبيت المتطلبات تلقائياً، استخدم:" "$YELLOW"
        print_colored "  ./run.sh --install" "$YELLOW"
        print_colored "أو:" "$YELLOW"
        print_colored "  pip3 install -r requirements.txt" "$YELLOW"
        echo
        read -p "هل تريد المتابعة بدون تثبيت المتطلبات؟ (y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            exit 1
        fi
    fi
fi

echo

# التحقق من ملفات التطبيق
print_colored "التحقق من ملفات التطبيق..." "$YELLOW"

REQUIRED_FILES=("main.py" "prayer.py" "settings.py" "utils.py")
for file in "${REQUIRED_FILES[@]}"; do
    if [ -f "$file" ]; then
        print_colored "✓ $file موجود" "$GREEN"
    else
        print_colored "✗ $file مفقود" "$RED"
        exit 1
    fi
done

# التحقق من مجلد الأصول
if [ -d "assets" ]; then
    print_colored "✓ مجلد assets موجود" "$GREEN"
    
    if [ -f "assets/config.json" ]; then
        print_colored "✓ ملف الإعدادات موجود" "$GREEN"
    else
        print_colored "⚠ ملف الإعدادات مفقود" "$YELLOW"
    fi
    
    if [ -f "assets/adhan.mp3" ]; then
        print_colored "✓ ملف الأذان موجود" "$GREEN"
    else
        print_colored "⚠ ملف الأذان مفقود - يمكن إضافته لاحقاً" "$YELLOW"
    fi
else
    print_colored "⚠ مجلد assets مفقود" "$YELLOW"
    mkdir -p assets
    print_colored "✓ تم إنشاء مجلد assets" "$GREEN"
fi

echo

# تشغيل التطبيق
print_colored "تشغيل التطبيق..." "$BLUE"
print_colored "للخروج، اضغط Ctrl+C أو أغلق التطبيق من شريط النظام" "$YELLOW"
echo

# تشغيل مع معالجة الأخطاء
if python3 main.py; then
    print_colored "تم إنهاء التطبيق بنجاح" "$GREEN"
else
    EXIT_CODE=$?
    print_colored "خطأ في تشغيل التطبيق (رمز الخطأ: $EXIT_CODE)" "$RED"
    
    # اقتراحات لحل المشاكل
    echo
    print_colored "اقتراحات لحل المشكلة:" "$YELLOW"
    print_colored "1. تأكد من تثبيت جميع المتطلبات: pip3 install -r requirements.txt" "$YELLOW"
    print_colored "2. تأكد من تشغيل بيئة سطح مكتب مدعومة" "$YELLOW"
    print_colored "3. تحقق من سجلات الأخطاء في ~/.local/share/prayer-times-app/logs/" "$YELLOW"
    print_colored "4. جرب تشغيل: python3 main.py مباشرة لرؤية رسائل الخطأ" "$YELLOW"
    
    exit $EXIT_CODE
fi
