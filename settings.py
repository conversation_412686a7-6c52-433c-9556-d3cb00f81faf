#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة إعدادات تطبيق أوقات الصلاة
Prayer Times Application Settings Window

تحتوي على:
- واجهة إعدادات المستخدم
- تحديد الموقع الجغرافي
- اختيار طريقة الحساب
- إعدادات التنبيهات والأذان
- حفظ وتحميل الإعدادات
"""

from typing import Dict, Any, Optional, Callable
from pathlib import Path

from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QLineEdit, QComboBox, QSpinBox, QCheckBox,
    QPushButton, QGroupBox, QFileDialog, QTabWidget,
    QWidget, QMessageBox, QDoubleSpinBox
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont, QIcon

from prayer import PrayerTimesCalculator


class LocationTab(QWidget):
    """تبويب إعدادات الموقع الجغرافي"""
    
    def __init__(self, settings: Dict[str, Any]):
        super().__init__()
        self.settings = settings
        self._init_ui()
        self._load_settings()
    
    def _init_ui(self):
        """تهيئة واجهة تبويب الموقع"""
        layout = QVBoxLayout()
        
        # مجموعة الموقع الجغرافي
        location_group = QGroupBox("الموقع الجغرافي")
        location_layout = QGridLayout()
        
        # TODO: إضافة حقول إدخال خط العرض والطول
        self.latitude_label = QLabel("خط العرض:")
        self.latitude_spinbox = QDoubleSpinBox()
        # TODO: تكوين QDoubleSpinBox للخط العرض (-90 إلى 90)
        
        self.longitude_label = QLabel("خط الطول:")
        self.longitude_spinbox = QDoubleSpinBox()
        # TODO: تكوين QDoubleSpinBox للخط الطول (-180 إلى 180)
        
        # TODO: إضافة حقول المدينة والدولة
        self.city_label = QLabel("المدينة:")
        self.city_edit = QLineEdit()
        
        self.country_label = QLabel("الدولة:")
        self.country_edit = QLineEdit()
        
        # TODO: قائمة المناطق الزمنية
        self.timezone_label = QLabel("المنطقة الزمنية:")
        self.timezone_combo = QComboBox()
        # TODO: ملء قائمة المناطق الزمنية
        
        # TODO: ترتيب العناصر في الشبكة
        location_layout.addWidget(self.latitude_label, 0, 0)
        location_layout.addWidget(self.latitude_spinbox, 0, 1)
        location_layout.addWidget(self.longitude_label, 1, 0)
        location_layout.addWidget(self.longitude_spinbox, 1, 1)
        location_layout.addWidget(self.city_label, 2, 0)
        location_layout.addWidget(self.city_edit, 2, 1)
        location_layout.addWidget(self.country_label, 3, 0)
        location_layout.addWidget(self.country_edit, 3, 1)
        location_layout.addWidget(self.timezone_label, 4, 0)
        location_layout.addWidget(self.timezone_combo, 4, 1)
        
        location_group.setLayout(location_layout)
        layout.addWidget(location_group)
        
        # TODO: زر للحصول على الموقع الحالي (GPS/IP)
        self.auto_location_btn = QPushButton("تحديد الموقع تلقائياً")
        # TODO: ربط الزر بدالة تحديد الموقع التلقائي
        layout.addWidget(self.auto_location_btn)
        
        layout.addStretch()
        self.setLayout(layout)
    
    def _load_settings(self):
        """تحميل إعدادات الموقع"""
        # TODO: تحميل القيم من الإعدادات إلى الحقول
        pass
    
    def get_settings(self) -> Dict[str, Any]:
        """الحصول على إعدادات الموقع الحالية"""
        # TODO: جمع القيم من الحقول وإرجاعها كقاموس
        return {
            "latitude": self.latitude_spinbox.value(),
            "longitude": self.longitude_spinbox.value(),
            "city": self.city_edit.text(),
            "country": self.country_edit.text(),
            "timezone": self.timezone_combo.currentText()
        }
    
    def auto_detect_location(self):
        """تحديد الموقع تلقائياً"""
        # TODO: استخدام خدمة تحديد الموقع (IP geolocation أو GPS)
        pass


class CalculationTab(QWidget):
    """تبويب إعدادات طريقة الحساب"""
    
    def __init__(self, settings: Dict[str, Any]):
        super().__init__()
        self.settings = settings
        self._init_ui()
        self._load_settings()
    
    def _init_ui(self):
        """تهيئة واجهة تبويب طريقة الحساب"""
        layout = QVBoxLayout()
        
        # مجموعة طريقة الحساب
        calc_group = QGroupBox("طريقة حساب أوقات الصلاة")
        calc_layout = QVBoxLayout()
        
        # TODO: قائمة طرق الحساب
        self.method_label = QLabel("طريقة الحساب:")
        self.method_combo = QComboBox()
        
        # TODO: ملء قائمة طرق الحساب من PrayerTimesCalculator
        methods = PrayerTimesCalculator.get_available_methods()
        for method_key, method_name in methods.items():
            self.method_combo.addItem(method_name, method_key)
        
        calc_layout.addWidget(self.method_label)
        calc_layout.addWidget(self.method_combo)
        
        # TODO: إعدادات إضافية لطريقة الحساب (اختيارية)
        self.adjustments_label = QLabel("تعديلات إضافية (دقائق):")
        
        # تعديل وقت الفجر
        fajr_layout = QHBoxLayout()
        self.fajr_adj_label = QLabel("الفجر:")
        self.fajr_adj_spinbox = QSpinBox()
        self.fajr_adj_spinbox.setRange(-30, 30)
        fajr_layout.addWidget(self.fajr_adj_label)
        fajr_layout.addWidget(self.fajr_adj_spinbox)
        fajr_layout.addStretch()
        
        # تعديل وقت العشاء
        isha_layout = QHBoxLayout()
        self.isha_adj_label = QLabel("العشاء:")
        self.isha_adj_spinbox = QSpinBox()
        self.isha_adj_spinbox.setRange(-30, 30)
        isha_layout.addWidget(self.isha_adj_label)
        isha_layout.addWidget(self.isha_adj_spinbox)
        isha_layout.addStretch()
        
        calc_layout.addWidget(self.adjustments_label)
        calc_layout.addLayout(fajr_layout)
        calc_layout.addLayout(isha_layout)
        
        calc_group.setLayout(calc_layout)
        layout.addWidget(calc_group)
        
        layout.addStretch()
        self.setLayout(layout)
    
    def _load_settings(self):
        """تحميل إعدادات طريقة الحساب"""
        # TODO: تحميل القيم من الإعدادات
        pass
    
    def get_settings(self) -> Dict[str, Any]:
        """الحصول على إعدادات طريقة الحساب"""
        # TODO: جمع القيم وإرجاعها
        return {
            "calculation_method": self.method_combo.currentData(),
            "fajr_adjustment": self.fajr_adj_spinbox.value(),
            "isha_adjustment": self.isha_adj_spinbox.value()
        }


class NotificationTab(QWidget):
    """تبويب إعدادات التنبيهات"""
    
    def __init__(self, settings: Dict[str, Any]):
        super().__init__()
        self.settings = settings
        self._init_ui()
        self._load_settings()
    
    def _init_ui(self):
        """تهيئة واجهة تبويب التنبيهات"""
        layout = QVBoxLayout()
        
        # مجموعة التنبيهات
        notif_group = QGroupBox("إعدادات التنبيهات")
        notif_layout = QVBoxLayout()
        
        # TODO: تفعيل/تعطيل التنبيهات
        self.enable_notifications_cb = QCheckBox("تفعيل التنبيهات")
        notif_layout.addWidget(self.enable_notifications_cb)
        
        # TODO: وقت التنبيه قبل الصلاة
        notif_time_layout = QHBoxLayout()
        self.notif_time_label = QLabel("التنبيه قبل الصلاة بـ:")
        self.notif_time_spinbox = QSpinBox()
        self.notif_time_spinbox.setRange(1, 60)
        self.notif_time_spinbox.setSuffix(" دقيقة")
        notif_time_layout.addWidget(self.notif_time_label)
        notif_time_layout.addWidget(self.notif_time_spinbox)
        notif_time_layout.addStretch()
        
        notif_layout.addLayout(notif_time_layout)
        notif_group.setLayout(notif_layout)
        layout.addWidget(notif_group)
        
        # مجموعة الأذان
        adhan_group = QGroupBox("إعدادات الأذان")
        adhan_layout = QVBoxLayout()
        
        # TODO: تفعيل/تعطيل الأذان
        self.enable_adhan_cb = QCheckBox("تشغيل الأذان")
        adhan_layout.addWidget(self.enable_adhan_cb)
        
        # TODO: اختيار ملف الأذان
        adhan_file_layout = QHBoxLayout()
        self.adhan_file_label = QLabel("ملف الأذان:")
        self.adhan_file_edit = QLineEdit()
        self.adhan_file_btn = QPushButton("تصفح...")
        # TODO: ربط الزر بدالة اختيار الملف
        
        adhan_file_layout.addWidget(self.adhan_file_label)
        adhan_file_layout.addWidget(self.adhan_file_edit)
        adhan_file_layout.addWidget(self.adhan_file_btn)
        
        adhan_layout.addLayout(adhan_file_layout)
        
        # TODO: مستوى الصوت
        volume_layout = QHBoxLayout()
        self.volume_label = QLabel("مستوى الصوت:")
        self.volume_spinbox = QSpinBox()
        self.volume_spinbox.setRange(0, 100)
        self.volume_spinbox.setSuffix("%")
        volume_layout.addWidget(self.volume_label)
        volume_layout.addWidget(self.volume_spinbox)
        volume_layout.addStretch()
        
        adhan_layout.addLayout(volume_layout)
        adhan_group.setLayout(adhan_layout)
        layout.addWidget(adhan_group)
        
        layout.addStretch()
        self.setLayout(layout)
    
    def _load_settings(self):
        """تحميل إعدادات التنبيهات"""
        # TODO: تحميل القيم من الإعدادات
        pass
    
    def get_settings(self) -> Dict[str, Any]:
        """الحصول على إعدادات التنبيهات"""
        # TODO: جمع القيم وإرجاعها
        return {
            "enable_notifications": self.enable_notifications_cb.isChecked(),
            "notification_minutes": self.notif_time_spinbox.value(),
            "enable_adhan": self.enable_adhan_cb.isChecked(),
            "adhan_sound_file": self.adhan_file_edit.text(),
            "volume": self.volume_spinbox.value()
        }
    
    def browse_adhan_file(self):
        """تصفح واختيار ملف الأذان"""
        # TODO: فتح حوار اختيار ملف صوتي
        pass


class SettingsWindow(QDialog):
    """نافذة إعدادات التطبيق الرئيسية"""
    
    # إشارة لإرسال الإعدادات المحدثة
    settings_changed = pyqtSignal(dict)
    
    def __init__(self, current_settings: Dict[str, Any], parent=None):
        super().__init__(parent)
        self.current_settings = current_settings.copy()
        self._init_ui()
        self._setup_connections()
    
    def _init_ui(self):
        """تهيئة واجهة نافذة الإعدادات"""
        self.setWindowTitle("إعدادات أوقات الصلاة")
        self.setModal(True)
        self.resize(500, 400)
        
        # TODO: تخطيط رئيسي
        layout = QVBoxLayout()
        
        # TODO: تبويبات الإعدادات
        self.tab_widget = QTabWidget()
        
        # تبويب الموقع
        self.location_tab = LocationTab(self.current_settings)
        self.tab_widget.addTab(self.location_tab, "الموقع")
        
        # تبويب طريقة الحساب
        self.calculation_tab = CalculationTab(self.current_settings)
        self.tab_widget.addTab(self.calculation_tab, "طريقة الحساب")
        
        # تبويب التنبيهات
        self.notification_tab = NotificationTab(self.current_settings)
        self.tab_widget.addTab(self.notification_tab, "التنبيهات")
        
        layout.addWidget(self.tab_widget)
        
        # TODO: أزرار الحفظ والإلغاء
        buttons_layout = QHBoxLayout()
        
        self.save_btn = QPushButton("حفظ")
        self.cancel_btn = QPushButton("إلغاء")
        self.apply_btn = QPushButton("تطبيق")
        
        buttons_layout.addStretch()
        buttons_layout.addWidget(self.save_btn)
        buttons_layout.addWidget(self.apply_btn)
        buttons_layout.addWidget(self.cancel_btn)
        
        layout.addLayout(buttons_layout)
        self.setLayout(layout)
    
    def _setup_connections(self):
        """ربط الإشارات بالدوال"""
        # TODO: ربط أزرار الحفظ والإلغاء والتطبيق
        self.save_btn.clicked.connect(self.save_and_close)
        self.apply_btn.clicked.connect(self.apply_settings)
        self.cancel_btn.clicked.connect(self.reject)
    
    def collect_all_settings(self) -> Dict[str, Any]:
        """جمع جميع الإعدادات من التبويبات"""
        # TODO: جمع الإعدادات من جميع التبويبات
        all_settings = self.current_settings.copy()
        
        # إعدادات الموقع
        location_settings = self.location_tab.get_settings()
        all_settings["location"] = location_settings
        
        # إعدادات طريقة الحساب
        calc_settings = self.calculation_tab.get_settings()
        all_settings.update(calc_settings)
        
        # إعدادات التنبيهات
        notif_settings = self.notification_tab.get_settings()
        all_settings.update(notif_settings)
        
        return all_settings
    
    def apply_settings(self):
        """تطبيق الإعدادات دون إغلاق النافذة"""
        # TODO: جمع الإعدادات وإرسال إشارة التحديث
        new_settings = self.collect_all_settings()
        self.settings_changed.emit(new_settings)
    
    def save_and_close(self):
        """حفظ الإعدادات وإغلاق النافذة"""
        # TODO: تطبيق الإعدادات ثم إغلاق النافذة
        self.apply_settings()
        self.accept()
    
    def validate_settings(self) -> bool:
        """التحقق من صحة الإعدادات"""
        # TODO: التحقق من صحة القيم المدخلة
        # مثل التأكد من صحة الإحداثيات ووجود ملف الأذان
        return True
