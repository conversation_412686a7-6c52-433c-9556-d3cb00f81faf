#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
نافذة إعدادات تطبيق أوقات الصلاة
Prayer Times Application Settings Window

تحتوي على:
- واجهة إعدادات المستخدم
- تحديد الموقع الجغرافي
- اختيار طريقة الحساب
- إعدادات التنبيهات والأذان
- حفظ وتحميل الإعدادات
"""

from typing import Dict, Any, Optional, Callable
from pathlib import Path

from PyQt6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QGridLayout,
    QLabel, QLineEdit, QComboBox, QSpinBox, QCheckBox,
    QPushButton, QGroupBox, QFileDialog, QTabWidget,
    QWidget, QMessageBox, QDoubleSpinBox
)
from PyQt6.QtCore import Qt, pyqtSignal
from PyQt6.QtGui import QFont, QIcon

from prayer import PrayerTimesCalculator


class LocationTab(QWidget):
    """تبويب إعدادات الموقع الجغرافي"""
    
    def __init__(self, settings: Dict[str, Any]):
        super().__init__()
        self.settings = settings
        self._init_ui()
        self._load_settings()
    
    def _init_ui(self):
        """تهيئة واجهة تبويب الموقع"""
        layout = QVBoxLayout()

        # مجموعة الموقع الجغرافي
        location_group = QGroupBox("الموقع الجغرافي")
        location_layout = QGridLayout()

        # خط العرض
        self.latitude_label = QLabel("خط العرض:")
        self.latitude_spinbox = QDoubleSpinBox()
        self.latitude_spinbox.setRange(-90.0, 90.0)
        self.latitude_spinbox.setDecimals(6)
        self.latitude_spinbox.setSingleStep(0.000001)

        # خط الطول
        self.longitude_label = QLabel("خط الطول:")
        self.longitude_spinbox = QDoubleSpinBox()
        self.longitude_spinbox.setRange(-180.0, 180.0)
        self.longitude_spinbox.setDecimals(6)
        self.longitude_spinbox.setSingleStep(0.000001)

        # المدينة
        self.city_label = QLabel("المدينة:")
        self.city_edit = QLineEdit()

        # الدولة
        self.country_label = QLabel("الدولة:")
        self.country_edit = QLineEdit()

        # المنطقة الزمنية
        self.timezone_label = QLabel("المنطقة الزمنية:")
        self.timezone_combo = QComboBox()
        self._populate_timezones()

        # ترتيب العناصر في الشبكة
        location_layout.addWidget(self.latitude_label, 0, 0)
        location_layout.addWidget(self.latitude_spinbox, 0, 1)
        location_layout.addWidget(self.longitude_label, 1, 0)
        location_layout.addWidget(self.longitude_spinbox, 1, 1)
        location_layout.addWidget(self.city_label, 2, 0)
        location_layout.addWidget(self.city_edit, 2, 1)
        location_layout.addWidget(self.country_label, 3, 0)
        location_layout.addWidget(self.country_edit, 3, 1)
        location_layout.addWidget(self.timezone_label, 4, 0)
        location_layout.addWidget(self.timezone_combo, 4, 1)

        location_group.setLayout(location_layout)
        layout.addWidget(location_group)

        # زر للحصول على الموقع الحالي
        self.auto_location_btn = QPushButton("تحديد الموقع تلقائياً")
        self.auto_location_btn.clicked.connect(self.auto_detect_location)
        layout.addWidget(self.auto_location_btn)

        layout.addStretch()
        self.setLayout(layout)

    def _populate_timezones(self):
        """ملء قائمة المناطق الزمنية"""
        import pytz

        # مناطق زمنية شائعة في المنطقة العربية
        common_timezones = [
            ("Asia/Riyadh", "الرياض (GMT+3)"),
            ("Asia/Dubai", "دبي (GMT+4)"),
            ("Asia/Kuwait", "الكويت (GMT+3)"),
            ("Asia/Qatar", "قطر (GMT+3)"),
            ("Asia/Bahrain", "البحرين (GMT+3)"),
            ("Asia/Baghdad", "بغداد (GMT+3)"),
            ("Africa/Cairo", "القاهرة (GMT+2)"),
            ("Asia/Damascus", "دمشق (GMT+2)"),
            ("Asia/Beirut", "بيروت (GMT+2)"),
            ("Asia/Amman", "عمان (GMT+2)"),
            ("Africa/Casablanca", "الدار البيضاء (GMT+1)"),
            ("Africa/Tunis", "تونس (GMT+1)"),
            ("Africa/Algiers", "الجزائر (GMT+1)"),
        ]

        for tz_id, tz_name in common_timezones:
            self.timezone_combo.addItem(tz_name, tz_id)
    
    def _load_settings(self):
        """تحميل إعدادات الموقع"""
        location = self.settings.get("location", {})

        self.latitude_spinbox.setValue(location.get("latitude", 31.7683))
        self.longitude_spinbox.setValue(location.get("longitude", 35.2137))
        self.city_edit.setText(location.get("city", "الرياض"))
        self.country_edit.setText(location.get("country", "السعودية"))

        # تحديد المنطقة الزمنية
        timezone = location.get("timezone", "Asia/Riyadh")
        for i in range(self.timezone_combo.count()):
            if self.timezone_combo.itemData(i) == timezone:
                self.timezone_combo.setCurrentIndex(i)
                break
    
    def get_settings(self) -> Dict[str, Any]:
        """الحصول على إعدادات الموقع الحالية"""
        # TODO: جمع القيم من الحقول وإرجاعها كقاموس
        return {
            "latitude": self.latitude_spinbox.value(),
            "longitude": self.longitude_spinbox.value(),
            "city": self.city_edit.text(),
            "country": self.country_edit.text(),
            "timezone": self.timezone_combo.currentText()
        }
    
    def auto_detect_location(self):
        """تحديد الموقع تلقائياً"""
        # TODO: استخدام خدمة تحديد الموقع (IP geolocation أو GPS)
        pass


class CalculationTab(QWidget):
    """تبويب إعدادات طريقة الحساب"""
    
    def __init__(self, settings: Dict[str, Any]):
        super().__init__()
        self.settings = settings
        self._init_ui()
        self._load_settings()
    
    def _init_ui(self):
        """تهيئة واجهة تبويب طريقة الحساب"""
        layout = QVBoxLayout()
        
        # مجموعة طريقة الحساب
        calc_group = QGroupBox("طريقة حساب أوقات الصلاة")
        calc_layout = QVBoxLayout()
        
        # TODO: قائمة طرق الحساب
        self.method_label = QLabel("طريقة الحساب:")
        self.method_combo = QComboBox()
        
        # TODO: ملء قائمة طرق الحساب من PrayerTimesCalculator
        methods = PrayerTimesCalculator.get_available_methods()
        for method_key, method_name in methods.items():
            self.method_combo.addItem(method_name, method_key)
        
        calc_layout.addWidget(self.method_label)
        calc_layout.addWidget(self.method_combo)
        
        # TODO: إعدادات إضافية لطريقة الحساب (اختيارية)
        self.adjustments_label = QLabel("تعديلات إضافية (دقائق):")
        
        # تعديل وقت الفجر
        fajr_layout = QHBoxLayout()
        self.fajr_adj_label = QLabel("الفجر:")
        self.fajr_adj_spinbox = QSpinBox()
        self.fajr_adj_spinbox.setRange(-30, 30)
        fajr_layout.addWidget(self.fajr_adj_label)
        fajr_layout.addWidget(self.fajr_adj_spinbox)
        fajr_layout.addStretch()
        
        # تعديل وقت العشاء
        isha_layout = QHBoxLayout()
        self.isha_adj_label = QLabel("العشاء:")
        self.isha_adj_spinbox = QSpinBox()
        self.isha_adj_spinbox.setRange(-30, 30)
        isha_layout.addWidget(self.isha_adj_label)
        isha_layout.addWidget(self.isha_adj_spinbox)
        isha_layout.addStretch()
        
        calc_layout.addWidget(self.adjustments_label)
        calc_layout.addLayout(fajr_layout)
        calc_layout.addLayout(isha_layout)
        
        calc_group.setLayout(calc_layout)
        layout.addWidget(calc_group)
        
        layout.addStretch()
        self.setLayout(layout)
    
    def _load_settings(self):
        """تحميل إعدادات طريقة الحساب"""
        # TODO: تحميل القيم من الإعدادات
        pass
    
    def get_settings(self) -> Dict[str, Any]:
        """الحصول على إعدادات طريقة الحساب"""
        # TODO: جمع القيم وإرجاعها
        return {
            "calculation_method": self.method_combo.currentData(),
            "fajr_adjustment": self.fajr_adj_spinbox.value(),
            "isha_adjustment": self.isha_adj_spinbox.value()
        }


class NotificationTab(QWidget):
    """تبويب إعدادات التنبيهات"""
    
    def __init__(self, settings: Dict[str, Any]):
        super().__init__()
        self.settings = settings
        self._init_ui()
        self._load_settings()
    
    def _init_ui(self):
        """تهيئة واجهة تبويب التنبيهات"""
        layout = QVBoxLayout()
        
        # مجموعة التنبيهات
        notif_group = QGroupBox("إعدادات التنبيهات")
        notif_layout = QVBoxLayout()
        
        # TODO: تفعيل/تعطيل التنبيهات
        self.enable_notifications_cb = QCheckBox("تفعيل التنبيهات")
        notif_layout.addWidget(self.enable_notifications_cb)
        
        # TODO: وقت التنبيه قبل الصلاة
        notif_time_layout = QHBoxLayout()
        self.notif_time_label = QLabel("التنبيه قبل الصلاة بـ:")
        self.notif_time_spinbox = QSpinBox()
        self.notif_time_spinbox.setRange(1, 60)
        self.notif_time_spinbox.setSuffix(" دقيقة")
        notif_time_layout.addWidget(self.notif_time_label)
        notif_time_layout.addWidget(self.notif_time_spinbox)
        notif_time_layout.addStretch()
        
        notif_layout.addLayout(notif_time_layout)
        notif_group.setLayout(notif_layout)
        layout.addWidget(notif_group)
        
        # مجموعة الأذان
        adhan_group = QGroupBox("إعدادات الأذان")
        adhan_layout = QVBoxLayout()
        
        # TODO: تفعيل/تعطيل الأذان
        self.enable_adhan_cb = QCheckBox("تشغيل الأذان")
        adhan_layout.addWidget(self.enable_adhan_cb)
        
        # TODO: اختيار ملف الأذان
        adhan_file_layout = QHBoxLayout()
        self.adhan_file_label = QLabel("ملف الأذان:")
        self.adhan_file_edit = QLineEdit()
        self.adhan_file_btn = QPushButton("تصفح...")
        # TODO: ربط الزر بدالة اختيار الملف
        
        adhan_file_layout.addWidget(self.adhan_file_label)
        adhan_file_layout.addWidget(self.adhan_file_edit)
        adhan_file_layout.addWidget(self.adhan_file_btn)
        
        adhan_layout.addLayout(adhan_file_layout)
        
        # TODO: مستوى الصوت
        volume_layout = QHBoxLayout()
        self.volume_label = QLabel("مستوى الصوت:")
        self.volume_spinbox = QSpinBox()
        self.volume_spinbox.setRange(0, 100)
        self.volume_spinbox.setSuffix("%")
        volume_layout.addWidget(self.volume_label)
        volume_layout.addWidget(self.volume_spinbox)
        volume_layout.addStretch()
        
        adhan_layout.addLayout(volume_layout)
        adhan_group.setLayout(adhan_layout)
        layout.addWidget(adhan_group)
        
        layout.addStretch()
        self.setLayout(layout)
    
    def _load_settings(self):
        """تحميل إعدادات التنبيهات"""
        # TODO: تحميل القيم من الإعدادات
        pass
    
    def get_settings(self) -> Dict[str, Any]:
        """الحصول على إعدادات التنبيهات"""
        # TODO: جمع القيم وإرجاعها
        return {
            "enable_notifications": self.enable_notifications_cb.isChecked(),
            "notification_minutes": self.notif_time_spinbox.value(),
            "enable_adhan": self.enable_adhan_cb.isChecked(),
            "adhan_sound_file": self.adhan_file_edit.text(),
            "volume": self.volume_spinbox.value()
        }
    
    def browse_adhan_file(self):
        """تصفح واختيار ملف الأذان"""
        # TODO: فتح حوار اختيار ملف صوتي
        pass


class GeneralTab(QWidget):
    """تبويب الإعدادات العامة"""

    def __init__(self, settings: Dict[str, Any]):
        super().__init__()
        self.settings = settings
        self._init_ui()
        self._load_settings()

    def _init_ui(self):
        """تهيئة واجهة تبويب الإعدادات العامة"""
        layout = QVBoxLayout()

        # مجموعة اللغة
        language_group = QGroupBox("اللغة")
        language_layout = QVBoxLayout()

        self.language_label = QLabel("اللغة المستعملة للعرض:")
        self.language_combo = QComboBox()
        self.language_combo.addItem("العربية", "ar")
        self.language_combo.addItem("English", "en")

        language_layout.addWidget(self.language_label)
        language_layout.addWidget(self.language_combo)
        language_group.setLayout(language_layout)
        layout.addWidget(language_group)

        # مجموعة إظهار العد التنازلي
        countdown_group = QGroupBox("إظهار العد التنازلي")
        countdown_layout = QVBoxLayout()

        self.countdown_desc = QLabel("تحديد ما إذا كان العد التنازلي يظهر حتى وقت الصلاة القادمة في ريط المهام")
        self.countdown_desc.setWordWrap(True)
        self.countdown_enable_cb = QCheckBox("تفعيل")

        countdown_layout.addWidget(self.countdown_desc)
        countdown_layout.addWidget(self.countdown_enable_cb)
        countdown_group.setLayout(countdown_layout)
        layout.addWidget(countdown_group)

        # مجموعة إظهار التوقيت
        timing_group = QGroupBox("إظهار التوقيت")
        timing_layout = QVBoxLayout()

        self.timing_desc = QLabel("تحديد ما إذا كانت التوقيت يظهر في العد التنازلي")
        self.timing_desc.setWordWrap(True)
        self.timing_enable_cb = QCheckBox("إيقاف التشغيل")

        timing_layout.addWidget(self.timing_desc)
        timing_layout.addWidget(self.timing_enable_cb)
        timing_group.setLayout(timing_layout)
        layout.addWidget(timing_group)

        # مجموعة الوضع المضغوط
        compact_group = QGroupBox("الوضع المضغوط")
        compact_layout = QVBoxLayout()

        self.compact_desc = QLabel("جعل الأداة أصغر وتحتل مساحة أقل")
        self.compact_desc.setWordWrap(True)
        self.compact_enable_cb = QCheckBox("إيقاف التشغيل")

        compact_layout.addWidget(self.compact_desc)
        compact_layout.addWidget(self.compact_enable_cb)
        compact_group.setLayout(compact_layout)
        layout.addWidget(compact_group)

        layout.addStretch()
        self.setLayout(layout)

    def _load_settings(self):
        """تحميل الإعدادات العامة"""
        ui_settings = self.settings.get("ui", {})

        # تحميل اللغة
        language = ui_settings.get("language", "ar")
        for i in range(self.language_combo.count()):
            if self.language_combo.itemData(i) == language:
                self.language_combo.setCurrentIndex(i)
                break

        # تحميل إعدادات العد التنازلي
        self.countdown_enable_cb.setChecked(ui_settings.get("show_countdown", True))
        self.timing_enable_cb.setChecked(not ui_settings.get("show_timing", True))
        self.compact_enable_cb.setChecked(not ui_settings.get("compact_mode", False))

    def get_settings(self) -> Dict[str, Any]:
        """الحصول على الإعدادات العامة"""
        return {
            "ui": {
                "language": self.language_combo.currentData(),
                "show_countdown": self.countdown_enable_cb.isChecked(),
                "show_timing": not self.timing_enable_cb.isChecked(),
                "compact_mode": not self.compact_enable_cb.isChecked()
            }
        }


class AdvancedTab(QWidget):
    """تبويب الإعدادات المتقدمة"""

    def __init__(self, settings: Dict[str, Any]):
        super().__init__()
        self.settings = settings
        self._init_ui()
        self._load_settings()

    def _init_ui(self):
        """تهيئة واجهة تبويب الإعدادات المتقدمة"""
        layout = QVBoxLayout()

        # مجموعة وقت التنبيه
        alert_group = QGroupBox("وقت التنبيه")
        alert_layout = QVBoxLayout()

        self.alert_desc = QLabel("عدد الدقائق التي تسبق دخول الوقت. القيمة 0 تعني تعطيل الإشعار")
        self.alert_desc.setWordWrap(True)

        alert_time_layout = QHBoxLayout()
        self.alert_spinbox = QSpinBox()
        self.alert_spinbox.setRange(0, 60)
        self.alert_spinbox.setValue(10)
        alert_time_layout.addWidget(self.alert_spinbox)
        alert_time_layout.addStretch()

        alert_layout.addWidget(self.alert_desc)
        alert_layout.addLayout(alert_time_layout)
        alert_group.setLayout(alert_layout)
        layout.addWidget(alert_group)

        # مجموعة فترة الوقت المفقود
        missed_group = QGroupBox("فترة الوقت المفقود")
        missed_layout = QVBoxLayout()

        self.missed_desc = QLabel("عدد الدقائق التي يتم خلالها تشغيل إشعار بعد دخول الوقت. القيمة 0 تعني تعطيل الإشعار")
        self.missed_desc.setWordWrap(True)

        missed_time_layout = QHBoxLayout()
        self.missed_spinbox = QSpinBox()
        self.missed_spinbox.setRange(0, 120)
        self.missed_spinbox.setValue(0)
        missed_time_layout.addWidget(self.missed_spinbox)
        missed_time_layout.addStretch()

        missed_layout.addWidget(self.missed_desc)
        missed_layout.addLayout(missed_time_layout)
        missed_group.setLayout(missed_layout)
        layout.addWidget(missed_group)

        # مجموعة نوع التقويم
        calendar_group = QGroupBox("نوع التقويم")
        calendar_layout = QVBoxLayout()

        self.calendar_desc = QLabel("التقويم الذي يستخدم لمعرفة التاريخ الهجري")
        self.calendar_desc.setWordWrap(True)

        self.calendar_combo = QComboBox()
        self.calendar_combo.addItem("أم القرى", "umm_al_qura")
        self.calendar_combo.addItem("التقويم الإسلامي العالمي", "islamic_world")

        calendar_layout.addWidget(self.calendar_desc)
        calendar_layout.addWidget(self.calendar_combo)
        calendar_group.setLayout(calendar_layout)
        layout.addWidget(calendar_group)

        layout.addStretch()
        self.setLayout(layout)

    def _load_settings(self):
        """تحميل الإعدادات المتقدمة"""
        advanced_settings = self.settings.get("advanced", {})
        notifications = self.settings.get("notifications", {})

        self.alert_spinbox.setValue(notifications.get("notification_minutes", 10))
        self.missed_spinbox.setValue(advanced_settings.get("missed_prayer_minutes", 0))

        calendar_type = advanced_settings.get("calendar_type", "umm_al_qura")
        for i in range(self.calendar_combo.count()):
            if self.calendar_combo.itemData(i) == calendar_type:
                self.calendar_combo.setCurrentIndex(i)
                break

    def get_settings(self) -> Dict[str, Any]:
        """الحصول على الإعدادات المتقدمة"""
        return {
            "notifications": {
                "notification_minutes": self.alert_spinbox.value()
            },
            "advanced": {
                "missed_prayer_minutes": self.missed_spinbox.value(),
                "calendar_type": self.calendar_combo.currentData()
            }
        }


class SettingsWindow(QDialog):
    """نافذة إعدادات التطبيق الرئيسية"""

    # إشارة لإرسال الإعدادات المحدثة
    settings_changed = pyqtSignal(dict)

    def __init__(self, current_settings: Dict[str, Any], parent=None):
        super().__init__(parent)
        self.current_settings = current_settings.copy()
        self._init_ui()
        self._setup_connections()

    def _init_ui(self):
        """تهيئة واجهة نافذة الإعدادات"""
        self.setWindowTitle("إعدادات أوقات الصلاة")
        self.setModal(True)
        self.resize(600, 500)

        # تطبيق تنسيق مشابه للتطبيق المرجعي
        self.setStyleSheet("""
            QDialog {
                background-color: #f8f9fa;
                font-family: 'Segoe UI', Tahoma, Arial, sans-serif;
                font-size: 11px;
            }
            QTabWidget::pane {
                border: 1px solid #dee2e6;
                background-color: white;
                border-radius: 4px;
            }
            QTabBar::tab {
                background-color: #e9ecef;
                padding: 8px 16px;
                margin-right: 2px;
                border-top-left-radius: 4px;
                border-top-right-radius: 4px;
            }
            QTabBar::tab:selected {
                background-color: white;
                border-bottom: 1px solid white;
            }
            QGroupBox {
                font-weight: bold;
                border: 1px solid #dee2e6;
                border-radius: 4px;
                margin-top: 8px;
                padding-top: 8px;
            }
            QGroupBox::title {
                subcontrol-origin: margin;
                left: 8px;
                padding: 0 4px 0 4px;
            }
            QPushButton {
                background-color: #007bff;
                color: white;
                border: none;
                padding: 8px 16px;
                border-radius: 4px;
                font-weight: bold;
            }
            QPushButton:hover {
                background-color: #0056b3;
            }
            QPushButton:pressed {
                background-color: #004085;
            }
        """)

        # تخطيط رئيسي
        layout = QVBoxLayout()

        # تبويبات الإعدادات
        self.tab_widget = QTabWidget()

        # تبويب عامة
        self.general_tab = GeneralTab(self.current_settings)
        self.tab_widget.addTab(self.general_tab, "عامة")

        # تبويب الموقع
        self.location_tab = LocationTab(self.current_settings)
        self.tab_widget.addTab(self.location_tab, "الموقع")

        # تبويب الأوقات
        self.calculation_tab = CalculationTab(self.current_settings)
        self.tab_widget.addTab(self.calculation_tab, "الأوقات")

        # تبويب الأذان
        self.notification_tab = NotificationTab(self.current_settings)
        self.tab_widget.addTab(self.notification_tab, "الأذان")

        # تبويب إعدادات متقدمة
        self.advanced_tab = AdvancedTab(self.current_settings)
        self.tab_widget.addTab(self.advanced_tab, "إعدادات متقدمة")

        layout.addWidget(self.tab_widget)

        # أزرار الحفظ والإلغاء
        buttons_layout = QHBoxLayout()

        self.save_btn = QPushButton("حفظ")
        self.cancel_btn = QPushButton("إلغاء")
        self.apply_btn = QPushButton("تطبيق")

        buttons_layout.addStretch()
        buttons_layout.addWidget(self.save_btn)
        buttons_layout.addWidget(self.apply_btn)
        buttons_layout.addWidget(self.cancel_btn)

        layout.addLayout(buttons_layout)
        self.setLayout(layout)
    
    def _setup_connections(self):
        """ربط الإشارات بالدوال"""
        # TODO: ربط أزرار الحفظ والإلغاء والتطبيق
        self.save_btn.clicked.connect(self.save_and_close)
        self.apply_btn.clicked.connect(self.apply_settings)
        self.cancel_btn.clicked.connect(self.reject)
    
    def collect_all_settings(self) -> Dict[str, Any]:
        """جمع جميع الإعدادات من التبويبات"""
        all_settings = self.current_settings.copy()

        # إعدادات عامة
        general_settings = self.general_tab.get_settings()
        self._deep_update(all_settings, general_settings)

        # إعدادات الموقع
        location_settings = self.location_tab.get_settings()
        all_settings["location"] = location_settings

        # إعدادات طريقة الحساب
        calc_settings = self.calculation_tab.get_settings()
        all_settings.update(calc_settings)

        # إعدادات التنبيهات
        notif_settings = self.notification_tab.get_settings()
        all_settings.update(notif_settings)

        # إعدادات متقدمة
        advanced_settings = self.advanced_tab.get_settings()
        self._deep_update(all_settings, advanced_settings)

        return all_settings

    def _deep_update(self, base_dict, update_dict):
        """تحديث عميق للقاموس"""
        for key, value in update_dict.items():
            if key in base_dict and isinstance(base_dict[key], dict) and isinstance(value, dict):
                self._deep_update(base_dict[key], value)
            else:
                base_dict[key] = value
    
    def apply_settings(self):
        """تطبيق الإعدادات دون إغلاق النافذة"""
        # TODO: جمع الإعدادات وإرسال إشارة التحديث
        new_settings = self.collect_all_settings()
        self.settings_changed.emit(new_settings)
    
    def save_and_close(self):
        """حفظ الإعدادات وإغلاق النافذة"""
        # TODO: تطبيق الإعدادات ثم إغلاق النافذة
        self.apply_settings()
        self.accept()
    
    def validate_settings(self) -> bool:
        """التحقق من صحة الإعدادات"""
        # TODO: التحقق من صحة القيم المدخلة
        # مثل التأكد من صحة الإحداثيات ووجود ملف الأذان
        return True
