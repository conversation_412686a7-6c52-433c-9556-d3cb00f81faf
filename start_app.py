#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
مشغل تطبيق أوقات الصلاة
Prayer Times Application Launcher
"""

import sys
import os
from pathlib import Path

def check_requirements():
    """التحقق من المتطلبات"""
    print("🔍 التحقق من المتطلبات...")
    
    required_modules = [
        'PyQt6',
        'APScheduler', 
        'pytz',
        'requests'
    ]
    
    missing = []
    for module in required_modules:
        try:
            __import__(module)
            print(f"✅ {module}")
        except ImportError:
            print(f"❌ {module} - غير مثبت")
            missing.append(module)
    
    if missing:
        print(f"\n⚠️ المتطلبات المفقودة: {', '.join(missing)}")
        print("لتثبيت المتطلبات:")
        print("pip install -r requirements.txt")
        return False
    
    return True

def check_files():
    """التحقق من الملفات المطلوبة"""
    print("\n🔍 التحقق من الملفات...")
    
    required_files = [
        "main.py",
        "prayer.py",
        "settings.py", 
        "utils.py"
    ]
    
    for file_name in required_files:
        if Path(file_name).exists():
            print(f"✅ {file_name}")
        else:
            print(f"❌ {file_name} - مفقود")
            return False
    
    # التحقق من مجلد الأصول
    assets_dir = Path("assets")
    if assets_dir.exists():
        print("✅ assets/")
        
        # التحقق من الأيقونة
        if (assets_dir / "icon.png").exists():
            print("✅ assets/icon.png")
        else:
            print("⚠️ assets/icon.png - مفقود (سيتم إنشاء أيقونة افتراضية)")
        
        # التحقق من ملف الصوت
        audio_files = ["adhan.wav", "adhan.mp3", "adhan.ogg"]
        found_audio = False
        for audio_file in audio_files:
            if (assets_dir / audio_file).exists():
                print(f"✅ assets/{audio_file}")
                found_audio = True
                break
        
        if not found_audio:
            print("⚠️ ملف الأذان - مفقود (لن يتم تشغيل الأذان)")
    else:
        print("⚠️ assets/ - مفقود (سيتم إنشاؤه)")
        assets_dir.mkdir(exist_ok=True)
    
    return True

def check_desktop_environment():
    """التحقق من بيئة سطح المكتب"""
    print("\n🔍 التحقق من بيئة سطح المكتب...")
    
    desktop_env = os.environ.get('XDG_CURRENT_DESKTOP', 'غير محدد')
    session_type = os.environ.get('XDG_SESSION_TYPE', 'غير محدد')
    
    print(f"   بيئة سطح المكتب: {desktop_env}")
    print(f"   نوع الجلسة: {session_type}")
    
    if 'KDE' in desktop_env.upper():
        print("✅ KDE Plasma - مثالي للتطبيق")
    elif 'GNOME' in desktop_env.upper():
        print("⚠️ GNOME - قد تحتاج لتمكين إضافات شريط النظام")
    else:
        print("⚠️ بيئة غير مدعومة رسمياً - قد يعمل التطبيق")
    
    return True

def start_application():
    """تشغيل التطبيق"""
    print("\n🚀 تشغيل تطبيق أوقات الصلاة...")
    print("للخروج: انقر بالزر الأيمن على الأيقونة واختر 'خروج'")
    print("أو اضغط Ctrl+C في هذه النافذة")
    print("-" * 50)
    
    try:
        # استيراد وتشغيل التطبيق
        from main import main
        main()
    except KeyboardInterrupt:
        print("\n\n✅ تم إنهاء التطبيق بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل التطبيق: {e}")
        print("\nللمساعدة:")
        print("1. تأكد من تثبيت جميع المتطلبات")
        print("2. تأكد من تشغيل بيئة سطح مكتب مدعومة")
        print("3. جرب تشغيل: python3 test_complete.py")
        return False
    
    return True

def main():
    """الدالة الرئيسية"""
    print("=" * 60)
    print("🕌 تطبيق أوقات الصلاة لـ KDE Plasma")
    print("Prayer Times Application for KDE Plasma")
    print("=" * 60)
    
    # التحقق من المتطلبات
    if not check_requirements():
        return 1
    
    # التحقق من الملفات
    if not check_files():
        return 1
    
    # التحقق من بيئة سطح المكتب
    check_desktop_environment()
    
    # تشغيل التطبيق
    if start_application():
        return 0
    else:
        return 1

if __name__ == "__main__":
    sys.exit(main())
