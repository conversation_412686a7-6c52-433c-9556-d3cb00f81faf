#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار أساسي للتطبيق
Basic Application Test

يختبر:
- استيراد الوحدات
- تهيئة الكلاسات الأساسية
- التحقق من المتطلبات
- اختبار الوظائف الأساسية
"""

import sys
import traceback
from pathlib import Path

def test_imports():
    """اختبار استيراد الوحدات المطلوبة"""
    print("🔍 اختبار استيراد الوحدات...")
    
    try:
        # اختبار PyQt6
        from PyQt6.QtWidgets import QApplication, QSystemTrayIcon
        from PyQt6.QtCore import QTimer
        from PyQt6.QtGui import QIcon
        print("✅ PyQt6 - تم الاستيراد بنجاح")
    except ImportError as e:
        print(f"❌ PyQt6 - فشل الاستيراد: {e}")
        return False
    
    try:
        # اختبار APScheduler
        from apscheduler.schedulers.qt import QtScheduler
        print("✅ APScheduler - تم الاستيراد بنجاح")
    except ImportError as e:
        print(f"❌ APScheduler - فشل الاستيراد: {e}")
        return False
    
    try:
        # اختبار pytz
        import pytz
        print("✅ pytz - تم الاستيراد بنجاح")
    except ImportError as e:
        print(f"❌ pytz - فشل الاستيراد: {e}")
        return False
    
    try:
        # اختبار مكتبة islam (اختيارية)
        import islam
        print("✅ islam - تم الاستيراد بنجاح")
    except ImportError:
        print("⚠️ islam - غير متاحة (ستحتاج لتثبيتها لاحقاً)")
    
    try:
        # اختبار الوحدات المحلية
        from prayer import PrayerTimesCalculator
        from settings import SettingsWindow
        from utils import ConfigManager
        print("✅ الوحدات المحلية - تم الاستيراد بنجاح")
    except ImportError as e:
        print(f"❌ الوحدات المحلية - فشل الاستيراد: {e}")
        return False
    
    return True

def test_system_tray():
    """اختبار دعم شريط النظام"""
    print("\n🔍 اختبار دعم شريط النظام...")
    
    try:
        from PyQt6.QtWidgets import QApplication, QSystemTrayIcon
        
        # إنشاء تطبيق مؤقت للاختبار
        app = QApplication([])
        
        if QSystemTrayIcon.isSystemTrayAvailable():
            print("✅ شريط النظام متاح")
            return True
        else:
            print("❌ شريط النظام غير متاح")
            return False
    except Exception as e:
        print(f"❌ خطأ في اختبار شريط النظام: {e}")
        return False

def test_config_manager():
    """اختبار مدير الإعدادات"""
    print("\n🔍 اختبار مدير الإعدادات...")
    
    try:
        from utils import ConfigManager
        
        # إنشاء مدير إعدادات
        config_manager = ConfigManager("test-prayer-app")
        
        # اختبار تحميل الإعدادات الافتراضية
        config = config_manager.load_config()
        
        if config and "location" in config:
            print("✅ تحميل الإعدادات الافتراضية")
            print(f"   الموقع: {config['location']['city']}")
            return True
        else:
            print("❌ فشل في تحميل الإعدادات")
            return False
    except Exception as e:
        print(f"❌ خطأ في اختبار مدير الإعدادات: {e}")
        return False

def test_prayer_calculator():
    """اختبار حاسبة أوقات الصلاة"""
    print("\n🔍 اختبار حاسبة أوقات الصلاة...")
    
    try:
        from prayer import PrayerTimesCalculator
        
        # إنشاء حاسبة مع إحداثيات الرياض
        calculator = PrayerTimesCalculator(
            latitude=24.7136,
            longitude=46.6753,
            timezone="Asia/Riyadh"
        )
        
        print("✅ تم إنشاء حاسبة أوقات الصلاة")
        
        # اختبار الحصول على طرق الحساب
        methods = calculator.get_available_methods()
        print(f"   طرق الحساب المتاحة: {len(methods)}")
        
        return True
    except Exception as e:
        print(f"❌ خطأ في اختبار حاسبة أوقات الصلاة: {e}")
        print(f"   التفاصيل: {traceback.format_exc()}")
        return False

def test_file_structure():
    """اختبار هيكل الملفات"""
    print("\n🔍 اختبار هيكل الملفات...")
    
    required_files = [
        "main.py",
        "prayer.py", 
        "settings.py",
        "utils.py",
        "requirements.txt",
        "README.md"
    ]
    
    required_dirs = [
        "assets"
    ]
    
    all_good = True
    
    # فحص الملفات المطلوبة
    for file_name in required_files:
        if Path(file_name).exists():
            print(f"✅ {file_name}")
        else:
            print(f"❌ {file_name} - مفقود")
            all_good = False
    
    # فحص المجلدات المطلوبة
    for dir_name in required_dirs:
        if Path(dir_name).is_dir():
            print(f"✅ {dir_name}/")
        else:
            print(f"❌ {dir_name}/ - مفقود")
            all_good = False
    
    # فحص ملفات الأصول
    assets_files = ["config.json"]
    for file_name in assets_files:
        file_path = Path("assets") / file_name
        if file_path.exists():
            print(f"✅ assets/{file_name}")
        else:
            print(f"⚠️ assets/{file_name} - مفقود (اختياري)")
    
    return all_good

def test_desktop_environment():
    """اختبار بيئة سطح المكتب"""
    print("\n🔍 اختبار بيئة سطح المكتب...")
    
    import os
    
    desktop_env = os.environ.get('XDG_CURRENT_DESKTOP', 'غير محدد')
    session_type = os.environ.get('XDG_SESSION_TYPE', 'غير محدد')
    
    print(f"   بيئة سطح المكتب: {desktop_env}")
    print(f"   نوع الجلسة: {session_type}")
    
    if 'KDE' in desktop_env.upper():
        print("✅ KDE Plasma مكتشف - مثالي للتطبيق")
        return True
    elif 'GNOME' in desktop_env.upper():
        print("⚠️ GNOME مكتشف - قد تحتاج لتمكين إضافات شريط النظام")
        return True
    else:
        print("⚠️ بيئة سطح مكتب غير مدعومة رسمياً")
        return True

def main():
    """الدالة الرئيسية للاختبار"""
    print("=" * 50)
    print("🧪 اختبار أساسي لتطبيق أوقات الصلاة")
    print("=" * 50)
    
    tests = [
        ("استيراد الوحدات", test_imports),
        ("هيكل الملفات", test_file_structure),
        ("بيئة سطح المكتب", test_desktop_environment),
        ("دعم شريط النظام", test_system_tray),
        ("مدير الإعدادات", test_config_manager),
        ("حاسبة أوقات الصلاة", test_prayer_calculator),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ خطأ غير متوقع في {test_name}: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 نتائج الاختبار: {passed}/{total} نجح")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! التطبيق جاهز للتطوير")
        return 0
    elif passed >= total * 0.7:
        print("⚠️ معظم الاختبارات نجحت، قد تحتاج لبعض التحسينات")
        return 1
    else:
        print("❌ فشلت عدة اختبارات، يجب حل المشاكل قبل المتابعة")
        return 2

if __name__ == "__main__":
    sys.exit(main())
