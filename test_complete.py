#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
اختبار شامل لتطبيق أوقات الصلاة
Comprehensive Test for Prayer Times Application
"""

import sys
import time
from pathlib import Path
from PyQt6.QtWidgets import QApplication
from PyQt6.QtCore import QTimer

def test_application_startup():
    """اختبار بدء تشغيل التطبيق"""
    print("🧪 اختبار بدء تشغيل التطبيق...")
    
    try:
        # استيراد التطبيق
        from main import PrayerApp
        
        # إنشاء QApplication
        app = QApplication([])
        app.setQuitOnLastWindowClosed(False)
        
        # إنشاء التطبيق
        prayer_app = PrayerApp()
        
        print("✅ تم إنشاء التطبيق بنجاح")
        
        # اختبار تحديث أوقات الصلاة
        prayer_app.update_prayer_times()
        print("✅ تم تحديث أوقات الصلاة")
        
        # اختبار عرض إشعار
        prayer_app.show_prayer_notification("fajr")
        print("✅ تم اختبار الإشعارات")
        
        # إنهاء التطبيق بعد ثانيتين
        QTimer.singleShot(2000, app.quit)
        
        # تشغيل لفترة قصيرة
        app.exec()
        
        print("✅ تم إنهاء التطبيق بنجاح")
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار التطبيق: {e}")
        return False

def test_prayer_calculator():
    """اختبار حاسبة أوقات الصلاة"""
    print("\n🧪 اختبار حاسبة أوقات الصلاة...")
    
    try:
        from prayer import PrayerTimesCalculator
        from datetime import date
        
        # إنشاء حاسبة
        calculator = PrayerTimesCalculator(
            latitude=24.7136,
            longitude=46.6753,
            timezone="Asia/Riyadh"
        )
        
        # حساب أوقات الصلاة
        prayer_times = calculator.get_prayer_times()
        print(f"✅ تم حساب أوقات الصلاة: {len(prayer_times)} صلوات")
        
        # تنسيق الأوقات
        formatted_times = calculator.format_prayer_times(prayer_times)
        print("✅ تم تنسيق الأوقات:")
        
        for prayer, time_str in formatted_times.items():
            arabic_name = calculator.get_prayer_name_arabic(prayer)
            print(f"   {arabic_name}: {time_str}")
        
        # اختبار الصلاة التالية
        next_prayer, next_time = calculator.get_next_prayer()
        next_arabic = calculator.get_prayer_name_arabic(next_prayer)
        print(f"✅ الصلاة التالية: {next_arabic} في {next_time.strftime('%H:%M')}")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار حاسبة أوقات الصلاة: {e}")
        return False

def test_config_manager():
    """اختبار مدير الإعدادات"""
    print("\n🧪 اختبار مدير الإعدادات...")
    
    try:
        from utils import ConfigManager
        
        # إنشاء مدير إعدادات
        config_manager = ConfigManager("test-prayer-app")
        
        # تحميل الإعدادات
        config = config_manager.load_config()
        print("✅ تم تحميل الإعدادات")
        print(f"   الموقع: {config.get('location', {}).get('city', 'غير محدد')}")
        
        # تعديل الإعدادات
        test_config = config.copy()
        test_config['test_key'] = 'test_value'
        
        # حفظ الإعدادات
        success = config_manager.save_config(test_config)
        if success:
            print("✅ تم حفظ الإعدادات")
        else:
            print("⚠️ فشل في حفظ الإعدادات")
        
        return True
        
    except Exception as e:
        print(f"❌ خطأ في اختبار مدير الإعدادات: {e}")
        return False

def test_files_structure():
    """اختبار هيكل الملفات"""
    print("\n🧪 اختبار هيكل الملفات...")
    
    required_files = [
        "main.py",
        "prayer.py",
        "settings.py",
        "utils.py",
        "assets/config.json",
        "assets/icon.png"
    ]
    
    optional_files = [
        "assets/adhan.wav",
        "assets/adhan.mp3"
    ]
    
    all_good = True
    
    for file_path in required_files:
        if Path(file_path).exists():
            print(f"✅ {file_path}")
        else:
            print(f"❌ {file_path} - مفقود")
            all_good = False
    
    for file_path in optional_files:
        if Path(file_path).exists():
            print(f"✅ {file_path} (اختياري)")
        else:
            print(f"⚠️ {file_path} - مفقود (اختياري)")
    
    return all_good

def test_audio_file():
    """اختبار ملف الصوت"""
    print("\n🧪 اختبار ملف الصوت...")
    
    try:
        from PyQt6.QtMultimedia import QMediaPlayer, QAudioOutput
        from PyQt6.QtCore import QUrl
        
        # البحث عن ملف صوتي
        audio_files = [
            "assets/adhan.wav",
            "assets/adhan.mp3",
            "assets/adhan.ogg"
        ]
        
        found_audio = None
        for audio_file in audio_files:
            if Path(audio_file).exists():
                found_audio = audio_file
                break
        
        if found_audio:
            print(f"✅ تم العثور على ملف صوتي: {found_audio}")
            
            # اختبار تحميل الملف
            app = QApplication.instance()
            if not app:
                app = QApplication([])
            
            player = QMediaPlayer()
            audio_output = QAudioOutput()
            player.setAudioOutput(audio_output)
            
            # تحميل الملف
            url = QUrl.fromLocalFile(str(Path(found_audio).absolute()))
            player.setSource(url)
            
            print("✅ تم تحميل الملف الصوتي بنجاح")
            return True
        else:
            print("⚠️ لم يتم العثور على ملف صوتي")
            return False
            
    except Exception as e:
        print(f"❌ خطأ في اختبار الملف الصوتي: {e}")
        return False

def main():
    """الدالة الرئيسية للاختبار الشامل"""
    print("=" * 60)
    print("🧪 اختبار شامل لتطبيق أوقات الصلاة")
    print("=" * 60)
    
    tests = [
        ("هيكل الملفات", test_files_structure),
        ("مدير الإعدادات", test_config_manager),
        ("حاسبة أوقات الصلاة", test_prayer_calculator),
        ("ملف الصوت", test_audio_file),
        ("بدء تشغيل التطبيق", test_application_startup),
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name}: نجح")
            else:
                print(f"❌ {test_name}: فشل")
        except Exception as e:
            print(f"❌ {test_name}: خطأ غير متوقع - {e}")
        
        print("-" * 40)
    
    print("\n" + "=" * 60)
    print(f"📊 نتائج الاختبار الشامل: {passed}/{total} نجح")
    
    if passed == total:
        print("🎉 جميع الاختبارات نجحت! التطبيق جاهز للاستخدام")
        print("\n📋 للتشغيل:")
        print("   python3 main.py")
        print("\n📋 أو استخدم:")
        print("   ./run.sh")
        return 0
    elif passed >= total * 0.8:
        print("⚠️ معظم الاختبارات نجحت، التطبيق قابل للاستخدام مع بعض التحسينات")
        return 1
    else:
        print("❌ فشلت عدة اختبارات، يجب حل المشاكل قبل الاستخدام")
        return 2

if __name__ == "__main__":
    sys.exit(main())
