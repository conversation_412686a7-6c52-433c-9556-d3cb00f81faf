#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
وحدة الأدوات المساعدة
Utility Functions Module

تحتوي على:
- دوال مساعدة للتطبيق
- إدارة الملفات والإعدادات
- دوال التحقق والتنسيق
- أدوات التشخيص والسجلات
"""

import json
import logging
import os
import sys
from pathlib import Path
from typing import Dict, Any, Optional, List, Tuple
from datetime import datetime, timedelta
import requests
from PyQt6.QtCore import QStandardPaths
from PyQt6.QtWidgets import QMessageBox


class ConfigManager:
    """مدير الإعدادات - تحميل وحفظ إعدادات التطبيق"""
    
    def __init__(self, app_name: str = "prayer-times-app"):
        self.app_name = app_name
        self.app_dir = Path(__file__).parent
        self.config_dir = self._get_config_directory()
        self.config_file = self.config_dir / "config.json"
        self.default_config_file = self.app_dir / "assets" / "config.json"
        
        # TODO: إنشاء مجلد الإعدادات إذا لم يكن موجوداً
        self._ensure_config_directory()
    
    def _get_config_directory(self) -> Path:
        """الحصول على مجلد إعدادات التطبيق"""
        # TODO: استخدام QStandardPaths للحصول على المجلد المناسب
        config_path = QStandardPaths.writableLocation(
            QStandardPaths.StandardLocation.ConfigLocation
        )
        return Path(config_path) / self.app_name
    
    def _ensure_config_directory(self):
        """التأكد من وجود مجلد الإعدادات"""
        # TODO: إنشاء المجلد إذا لم يكن موجوداً
        self.config_dir.mkdir(parents=True, exist_ok=True)
    
    def load_config(self) -> Dict[str, Any]:
        """تحميل الإعدادات من الملف"""
        # TODO: تحميل الإعدادات مع التعامل مع الأخطاء
        try:
            if self.config_file.exists():
                with open(self.config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            else:
                # تحميل الإعدادات الافتراضية
                return self._load_default_config()
        except Exception as e:
            logging.error(f"خطأ في تحميل الإعدادات: {e}")
            return self._load_default_config()
    
    def _load_default_config(self) -> Dict[str, Any]:
        """تحميل الإعدادات الافتراضية"""
        # TODO: تحميل من ملف الإعدادات الافتراضي
        try:
            if self.default_config_file.exists():
                with open(self.default_config_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            logging.error(f"خطأ في تحميل الإعدادات الافتراضية: {e}")
        
        # إعدادات افتراضية مُدمجة
        return {
            "location": {
                "latitude": 31.7683,
                "longitude": 35.2137,
                "city": "الرياض",
                "country": "السعودية",
                "timezone": "Asia/Riyadh"
            },
            "calculation": {
                "method": "muslim_league"
            },
            "notifications": {
                "enable_notifications": True,
                "notification_minutes": 5,
                "enable_adhan": True,
                "volume": 70
            }
        }
    
    def save_config(self, config: Dict[str, Any]) -> bool:
        """حفظ الإعدادات في الملف"""
        # TODO: حفظ الإعدادات مع التعامل مع الأخطاء
        try:
            with open(self.config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            logging.error(f"خطأ في حفظ الإعدادات: {e}")
            return False


class LocationService:
    """خدمة تحديد الموقع الجغرافي"""
    
    @staticmethod
    def get_location_by_ip() -> Optional[Dict[str, Any]]:
        """الحصول على الموقع باستخدام عنوان IP"""
        # TODO: استخدام خدمة تحديد الموقع عبر IP
        try:
            # يمكن استخدام خدمات مثل ipapi.co أو ipinfo.io
            response = requests.get("http://ipapi.co/json/", timeout=5)
            if response.status_code == 200:
                data = response.json()
                return {
                    "latitude": data.get("latitude"),
                    "longitude": data.get("longitude"),
                    "city": data.get("city"),
                    "country": data.get("country_name"),
                    "timezone": data.get("timezone")
                }
        except Exception as e:
            logging.error(f"خطأ في تحديد الموقع: {e}")
        
        return None
    
    @staticmethod
    def validate_coordinates(latitude: float, longitude: float) -> bool:
        """التحقق من صحة الإحداثيات الجغرافية"""
        # TODO: التحقق من نطاق خط العرض والطول
        return (-90 <= latitude <= 90) and (-180 <= longitude <= 180)


class NotificationHelper:
    """مساعد الإشعارات"""
    
    @staticmethod
    def show_system_notification(title: str, message: str, 
                                icon_path: Optional[str] = None):
        """عرض إشعار نظام"""
        # TODO: عرض إشعار نظام باستخدام أدوات Linux
        try:
            import subprocess
            cmd = ["notify-send", title, message]
            if icon_path and os.path.exists(icon_path):
                cmd.extend(["-i", icon_path])
            subprocess.run(cmd, check=False)
        except Exception as e:
            logging.error(f"خطأ في عرض الإشعار: {e}")
    
    @staticmethod
    def show_qt_notification(title: str, message: str, parent=None):
        """عرض إشعار باستخدام Qt"""
        # TODO: عرض رسالة باستخدام QMessageBox
        msg_box = QMessageBox(parent)
        msg_box.setWindowTitle(title)
        msg_box.setText(message)
        msg_box.setIcon(QMessageBox.Icon.Information)
        msg_box.exec()


class TimeFormatter:
    """مُنسق الأوقات"""
    
    @staticmethod
    def format_time(dt: datetime, format_12h: bool = True, 
                   show_seconds: bool = False) -> str:
        """تنسيق الوقت للعرض"""
        # TODO: تنسيق الوقت حسب التفضيلات
        if format_12h:
            if show_seconds:
                return dt.strftime("%I:%M:%S %p")
            else:
                return dt.strftime("%I:%M %p")
        else:
            if show_seconds:
                return dt.strftime("%H:%M:%S")
            else:
                return dt.strftime("%H:%M")
    
    @staticmethod
    def format_time_remaining(td: timedelta) -> str:
        """تنسيق الوقت المتبقي"""
        # TODO: تنسيق الوقت المتبقي بشكل مقروء
        total_seconds = int(td.total_seconds())
        
        if total_seconds < 0:
            return "انتهى الوقت"
        
        hours = total_seconds // 3600
        minutes = (total_seconds % 3600) // 60
        
        if hours > 0:
            return f"{hours} ساعة و {minutes} دقيقة"
        else:
            return f"{minutes} دقيقة"


class AudioHelper:
    """مساعد الصوتيات"""
    
    @staticmethod
    def get_supported_audio_formats() -> List[str]:
        """الحصول على تنسيقات الصوت المدعومة"""
        # TODO: إرجاع قائمة بتنسيقات الصوت المدعومة في Qt
        return [".mp3", ".wav", ".ogg", ".m4a", ".flac"]
    
    @staticmethod
    def validate_audio_file(file_path: str) -> bool:
        """التحقق من صحة ملف الصوت"""
        # TODO: التحقق من وجود الملف ونوعه
        if not os.path.exists(file_path):
            return False
        
        file_ext = os.path.splitext(file_path)[1].lower()
        return file_ext in AudioHelper.get_supported_audio_formats()


class LoggingSetup:
    """إعداد نظام السجلات"""
    
    @staticmethod
    def setup_logging(app_name: str = "prayer-times-app", 
                     log_level: int = logging.INFO):
        """إعداد نظام السجلات"""
        # TODO: إعداد نظام السجلات مع ملف وكونسول
        
        # مجلد السجلات
        log_dir = Path.home() / ".local" / "share" / app_name / "logs"
        log_dir.mkdir(parents=True, exist_ok=True)
        
        # ملف السجل
        log_file = log_dir / f"{app_name}.log"
        
        # تكوين السجلات
        logging.basicConfig(
            level=log_level,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler(log_file, encoding='utf-8'),
                logging.StreamHandler(sys.stdout)
            ]
        )
        
        logging.info(f"تم تهيئة نظام السجلات - ملف السجل: {log_file}")


class SystemTrayChecker:
    """فاحص دعم شريط النظام"""
    
    @staticmethod
    def check_system_tray_support() -> Tuple[bool, str]:
        """التحقق من دعم شريط النظام"""
        # TODO: التحقق من دعم النظام لشريط النظام
        from PyQt6.QtWidgets import QSystemTrayIcon
        
        if not QSystemTrayIcon.isSystemTrayAvailable():
            return False, "شريط النظام غير متاح في هذا النظام"
        
        # TODO: فحوصات إضافية لـ KDE Plasma
        desktop_env = os.environ.get('XDG_CURRENT_DESKTOP', '').lower()
        if 'kde' in desktop_env:
            return True, "KDE Plasma مدعوم"
        elif 'gnome' in desktop_env:
            return True, "GNOME مدعوم (قد تحتاج إلى تمكين الإضافات)"
        else:
            return True, f"بيئة سطح المكتب: {desktop_env}"


def get_app_version() -> str:
    """الحصول على إصدار التطبيق"""
    # TODO: قراءة الإصدار من ملف أو متغير
    return "1.0.0-dev"


def get_app_info() -> Dict[str, str]:
    """الحصول على معلومات التطبيق"""
    # TODO: إرجاع معلومات التطبيق
    return {
        "name": "تطبيق أوقات الصلاة",
        "version": get_app_version(),
        "description": "تطبيق سطح مكتب لعرض أوقات الصلاة في KDE Plasma",
        "author": "فريق التطوير",
        "license": "MIT"
    }
